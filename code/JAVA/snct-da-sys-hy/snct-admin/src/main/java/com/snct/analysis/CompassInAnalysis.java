package com.snct.analysis;

import com.snct.kafka.KafkaMessage;
import com.snct.analysis.vo.CompassInHbaseVo;
import com.snct.common.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: Attitude  一秒多组
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class CompassInAnalysis {
    protected static Logger logger = LoggerFactory.getLogger(CompassInAnalysis.class);


    /**
     *
     *
     * @param kafkaMessage
     * @return
     */
    public synchronized static CompassInHbaseVo getCompassNew(KafkaMessage kafkaMessage) {

        CompassInHbaseVo compassNewHbaseVo = null;

        try {
            //当前时间
            long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage.getInitialTime();
            if (StringUtils.isNotBlank(kafkaMessage.getMsg()) && kafkaMessage.getMsg().length()>=26){
                if (kafkaMessage.getMsg().substring(0,6).equalsIgnoreCase("000308")){
                    compassNewHbaseVo = new CompassInHbaseVo();
                    compassNewHbaseVo.setInitialTime(DateUtils.fetchWholeSecond(currentTime).toString());
                    List<String> gpars = new ArrayList<>();
                    for (int i = 0; i < kafkaMessage.getMsg().length(); i=i+2) {
                        gpars.add(kafkaMessage.getMsg().substring(i,i+2));
                    }
                    compassNewHbaseVo = getCompassMsg(gpars,compassNewHbaseVo);
                }else{

                }
            }


        } catch (Exception e) {
            logger.error("解析出错,---{}", e);
        }

        return compassNewHbaseVo;
    }

    public static CompassInHbaseVo getCompassMsg(List<String> arg,CompassInHbaseVo compassNewHbaseVo){

        Integer chiX1 = getChar2Integer(arg.get(3));
        Integer chiX2 = getChar2Integer(arg.get(4));
        BigDecimal bX = new BigDecimal((chiX1*256+chiX2)*13);
        compassNewHbaseVo.setMagneticXios(bX.doubleValue()/100+"");
        Integer chiY1 = getChar2Integer(arg.get(5));
        Integer chiY2 = getChar2Integer(arg.get(6));
        BigDecimal bY = new BigDecimal((chiY1*256+chiY2)*13);
        compassNewHbaseVo.setMagneticYios(bY.doubleValue()/100+"");
        Integer chiZ1 = getChar2Integer(arg.get(7));
        Integer chiZ2 = getChar2Integer(arg.get(8));
        BigDecimal bZ = new BigDecimal((chiZ1*256+chiZ2)*13);
        compassNewHbaseVo.setMagneticZios(bZ.doubleValue()/100+"");
        Integer chiC1 = getChar2Integer(arg.get(9));
        Integer chiC2 = getChar2Integer(arg.get(10));
//        BigDecimal bC = new BigDecimal(chiC1*256+chiC2);
        if((arg.get(9)+arg.get(10)).compareTo("8000")>0){
            //负数
            BigDecimal bC = new BigDecimal(chiC1*256+chiC2);
            compassNewHbaseVo.setHehdt((180+(bC.doubleValue()-65536)/10)+"");
        }else{
            BigDecimal bC = new BigDecimal(chiC1*256+chiC2);
            compassNewHbaseVo.setHehdt((180+bC.doubleValue()/10)+"");
        }

        return compassNewHbaseVo;
    }
    public static Integer getChar2Integer(String inter){
        Integer h = Integer.valueOf(inter, 16);
        return h;
    }

    public synchronized static Object getCompassParseNew(KafkaMessage kafkaMessage) {
        CompassInHbaseVo compassNewHbaseVo = null;

        try {
            //当前时间
            long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage.getInitialTime();
            if (StringUtils.isNotBlank(kafkaMessage.getMsg()) && kafkaMessage.getMsg().length()>=26){
                if (kafkaMessage.getMsg().substring(0,6).equalsIgnoreCase("000308")){
                    compassNewHbaseVo = new CompassInHbaseVo();
                    compassNewHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss").toString());
                    List<String> gpars = new ArrayList<>();
                    for (int i = 0; i < kafkaMessage.getMsg().length(); i=i+2) {
                        gpars.add(kafkaMessage.getMsg().substring(i,i+2));
                    }
                    compassNewHbaseVo = getCompassMsg(gpars,compassNewHbaseVo);
                }else{

                }
            }


        } catch (Exception e) {
            logger.error("解析出错,---{}", e);
        }

        return compassNewHbaseVo;
    }
}
