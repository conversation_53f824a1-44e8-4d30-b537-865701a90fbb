package com.snct.analysis;

import com.snct.kafka.KafkaMessage;
import com.snct.analysis.vo.DetectorHbaseVo;
import com.snct.common.utils.DateUtils;
import com.snct.analysis.domain.detector.Sddbt;
import com.snct.analysis.domain.detector.Sddpt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 探测仪
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class DetectorAnalysis {
    protected static Logger logger = LoggerFactory.getLogger(DetectorAnalysis.class);

    private static final String TEMP_KEY = "DETECTOR_TEMP";

    /**
     * 从一组探测仪数据中，取得有用的字段
     * 判断数据是否同一秒，同一秒则只取其中一组
     *
     * @param kafkaMessage
     * @return
     */
    public static DetectorHbaseVo getDetectorList(KafkaMessage kafkaMessage) {
        DetectorHbaseVo detectorHbaseVo = null;

        List<String> temp = BaseAnalysis.getTempList(TEMP_KEY, kafkaMessage.getCode());
        if (temp == null) {
            temp = new ArrayList<>();
        }

        try {
            long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage.getInitialTime();

            // 判断时间是否为同一秒
//            boolean isSame = BaseAnalysis.isSameTime(TEMP_KEY, kafkaMessage.getCode(), currentTime);

            // 同一秒的数据不做发送，在下一秒后再一起合并
            if (!CollectionUtils.isEmpty(temp) && kafkaMessage.getMsg().startsWith("$SDDBT") ) {
                detectorHbaseVo = new DetectorHbaseVo();
                detectorHbaseVo.setInitialTime(DateUtils.fetchWholeSecond(currentTime).toString());
                for (String line : temp) {
                    translateLine(detectorHbaseVo, line);
                }
                temp.clear();
            }

            temp.add(kafkaMessage.getMsg());

            BaseAnalysis.updateTemp(TEMP_KEY, kafkaMessage.getCode(), temp, currentTime);
        } catch (Exception e) {
            logger.error("detector探测仪解析出错,---{}", e);
            temp.clear();
        }
        return detectorHbaseVo;
    }
    /**
     * 解析预览
     *
     * @param kafkaMessage
     * @return
     */
    public static DetectorHbaseVo getParseDetectorList(List<KafkaMessage> kafkaMessage) {
        DetectorHbaseVo detectorHbaseVo = null;

        List<String> temp = new ArrayList<>();
        for (KafkaMessage kafkaMessage1:
                kafkaMessage) {
            if (kafkaMessage1.getMsg().startsWith("$SDDBT")){
                temp.add(kafkaMessage1.getMsg());
            }
        }

        try {
//            long currentTime = kafkaMessage.get(0).getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage.get(0).getInitialTime();
                detectorHbaseVo = new DetectorHbaseVo();
//            detectorHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(currentTime,"yyyy-MM-dd HH:mm:ss").toString());
                for (String line : temp) {
                    translateLine(detectorHbaseVo, line);
                }
            if (AwsAnalysis.checkObjAllFieldsIsNull(detectorHbaseVo)){
                detectorHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss").toString());
                detectorHbaseVo.setInitialTime(null);
            }else{
                return null;
            }
                temp.clear();

        } catch (Exception e) {
            logger.error("detector探测仪解析出错,---{}", e);
            temp.clear();
        }
        return detectorHbaseVo;
    }
    /**
     * 转换一行数据
     *
     * @param line
     * @return
     */
    private static void translateLine(DetectorHbaseVo detectorHbaseVo, String line) {

        String prefix = line.substring(0, 6);
        switch (prefix) {
            case "$SDDBT": {
                Sddbt sddbt = new Sddbt();
                sddbt.dataAnalysis(line);

                BeanUtils.copyProperties(sddbt, detectorHbaseVo);
            }
            break;
            case "$SDDPT": {
                Sddpt sddpt = new Sddpt();
                sddpt.dataAnalysis(line);

                detectorHbaseVo.setOffset(sddpt.getOffset());
            }
            break;
            default: {
            }
        }
    }
}
