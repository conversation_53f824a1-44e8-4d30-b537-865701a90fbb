package com.snct.analysis;

import com.snct.kafka.KafkaMessage;
import com.snct.analysis.vo.LogHbaseVo;
import com.snct.common.utils.DateUtils;
import com.snct.analysis.domain.log.Vdmtw;
import com.snct.analysis.domain.log.Vdvbw;
import com.snct.analysis.domain.log.Vdvlw;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: Log  计程仪
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class LogAnalysis {
    protected static Logger logger = LoggerFactory.getLogger(LogAnalysis.class);

    private static final String TEMP_KEY = "LOG_TEMP";

    /**
     * 从一组aws数据中，取得有用的字段
     * 判断数据是否同一秒，同一秒则只取其中一组
     *
     * @param kafkaMessage
     * @return
     */
    public static LogHbaseVo getLogList(KafkaMessage kafkaMessage) {
        LogHbaseVo logHbaseVo = null;

        List<String> temp = BaseAnalysis.getTempList(TEMP_KEY, kafkaMessage.getCode());
        if (temp == null) {
            temp = new ArrayList<>();
        }

        try {
            long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage.getInitialTime();

            // 判断时间是否为同一秒
            boolean isSame = BaseAnalysis.isSameTime(TEMP_KEY, kafkaMessage.getCode(), currentTime);

            // 同一秒的数据不做发送，在下一秒后再一起合并
            if (!CollectionUtils.isEmpty(temp) && kafkaMessage.getMsg().startsWith("$VDVBW")) {
                logHbaseVo = new LogHbaseVo();
                logHbaseVo.setInitialTime(DateUtils.fetchWholeSecond(currentTime).toString());
                for (String line : temp) {
                    translateLine(logHbaseVo, line);
                }
                temp.clear();
            }

            temp.add(kafkaMessage.getMsg());

            BaseAnalysis.updateTemp(TEMP_KEY, kafkaMessage.getCode(), temp, currentTime);
        } catch (Exception e) {
            logger.error("log解析出错,---{}", e);
            temp.clear();
        }
        return logHbaseVo;
    }

    /**解析预览
     * 从一组aws数据中，取得有用的字段
     * 判断数据是否同一秒，同一秒则只取其中一组
     *
     * @param kafkaMessage
     * @return
     */
    public static LogHbaseVo getParseLogList(List<KafkaMessage> kafkaMessage) {
        LogHbaseVo logHbaseVo = null;

        List<String> temp = new ArrayList<>();
        for (KafkaMessage kafkaMessage1:
                kafkaMessage) {
//            if (kafkaMessage1.getMsg().startsWith("$VDVBW")){
                temp.add(kafkaMessage1.getMsg());
//            }
        }
        try {
//            long currentTime = kafkaMessage.get(0).getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage.get(0).getInitialTime();

                logHbaseVo = new LogHbaseVo();
//                logHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(currentTime,"yyyy-MM-dd HH:mm:ss").toString());
                for (String line : temp) {
                    translateLine(logHbaseVo, line);
                }
            if (AwsAnalysis.checkObjAllFieldsIsNull(logHbaseVo)){
                logHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss").toString());
                logHbaseVo.setInitialTime(null);
            }else{
                return null;
            }
                temp.clear();

        } catch (Exception e) {
            logger.error("log解析出错,---{}", e);
            temp.clear();
        }
        return logHbaseVo;
    }

    /**
     * 转换一行数据
     *
     * @param line
     * @return
     */
    private static void translateLine(LogHbaseVo logHbaseVo, String line) {

        String prefix = line.substring(0, 6);
        switch (prefix) {
            case "$VDVBW": {
                Vdvbw vdvbw = new Vdvbw();
                vdvbw.dataAnalysis(line);

                BeanUtils.copyProperties(vdvbw, logHbaseVo);
            }
            break;
            case "$VDVLW": {
                Vdvlw vdvlw = new Vdvlw();
                vdvlw.dataAnalysis(line);

                BeanUtils.copyProperties(vdvlw, logHbaseVo);
            }
            break;
            case "$VDMTW": {
                Vdmtw vdmtw = new Vdmtw();
                vdmtw.dataAnalysis(line);

                BeanUtils.copyProperties(vdmtw, logHbaseVo);
            }
            break;
            default: {
            }
        }
    }
}
