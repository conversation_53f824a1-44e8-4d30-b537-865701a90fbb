package com.snct.analysis;

import com.snct.kafka.KafkaMessage;
import com.snct.analysis.vo.Sbe21HbaseVo;
import com.snct.common.utils.DateUtils;
import com.snct.analysis.domain.sbe21.Scan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * @description:
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class Sbe21Analysis {
    protected static Logger logger = LoggerFactory.getLogger(Sbe21Analysis.class);

    /**
     * 从一组sbe21数据中，取得有用的字段
     *
     * @param kafkaMessage
     * @return
     */
    public static Sbe21HbaseVo getSbe21List(KafkaMessage kafkaMessage) {

        Sbe21HbaseVo sbe21HbaseVo = null;
        if (StringUtils.isEmpty(kafkaMessage.getMsg())) {
            return null;
        }
        try {
            if (kafkaMessage.getMsg().startsWith("<Sc")) {
                sbe21HbaseVo = new Sbe21HbaseVo();
                // 转换一个完整sbe21数据
                translateLine(sbe21HbaseVo, kafkaMessage);
            }
        } catch (Exception e) {
            logger.error("sbe21解析出错,---{}", e);
        }
        return sbe21HbaseVo;
    }

    /**
     * 解析预览--目前只解析返回一条
     *
     * @param kafkaMessage
     * @return
     */
    public static Sbe21HbaseVo getParseSbe21List(List<KafkaMessage> kafkaMessage) {

        Sbe21HbaseVo sbe21HbaseVo = null;

        try {
            for (KafkaMessage kafkaMessage1:
                    kafkaMessage) {
                if (kafkaMessage1.getMsg().startsWith("<Sc")) {
                    sbe21HbaseVo = new Sbe21HbaseVo();
                    // 转换一个完整sbe21数据
                    translateLine(sbe21HbaseVo, kafkaMessage1);
                }
            }
            if (AwsAnalysis.checkObjAllFieldsIsNull(sbe21HbaseVo)){
                sbe21HbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss").toString());
                sbe21HbaseVo.setInitialTime(null);
            }else{
                return null;
            }
        } catch (Exception e) {
            logger.error("sbe21解析出错,---{}", e);
        }
        return sbe21HbaseVo;
    }

    /**
     * 转换一行数据
     *
     * @param kafkaMessage
     * @return
     */
    private static void translateLine(Sbe21HbaseVo sbe21HbaseVo, KafkaMessage kafkaMessage) {

        String prefix = kafkaMessage.getMsg().substring(0, 3);
        if ("<Sc".equals(prefix)) {
            Scan scan = new Scan();
            long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() : kafkaMessage.getInitialTime();
            scan.dataAnalysis(kafkaMessage.getMsg());
            BeanUtils.copyProperties(scan, sbe21HbaseVo);

            sbe21HbaseVo.setInitialTime(DateUtils.fetchWholeSecond(currentTime).toString());
        }
    }

}
