//package com.snct.analysis.domain;
//
///**
// * @description: 仪器类抽象类
// * <AUTHOR>
// * @date 2025-04-11
// **/
//public abstract class Instrument {
//
//    /**
//     * Sentence data fields delimiter char
//     */
//    char FIELD_DELIMITER = ',';
//
//
//    /**
//     * \
//     * 对各自的原始数据存储
//     *
//     * @param dataStr 接收到仪器发送的数据
//     */
//    public abstract void dataAnalysis(String dataStr);
//
//    public String[] valuesTrim(String[] values) {
//        String[] result = new String[values.length];
//
//        for (int i = 0; i < values.length; i++) {
//            result[i] = values[i].trim();
//        }
//
//        return result;
//    }
//}
