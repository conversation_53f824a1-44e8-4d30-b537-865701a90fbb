package com.snct.analysis.domain.compass;

import com.snct.system.domain.msg.Instrument;

/**
 * @description: hehdt的hehdt数据
 * Example:$HEROT,001.5,A*2F
 * $HEROT,1,2
 * 逗號隔開
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class Herot extends Instrument {
    /**
     * 1.转向速率  Rate Of Turn负数表示向左舷转
     */
    private String turningRate;
    /**
     * 2.状态位 A=数据是有效的+校驗碼
     */
    private String dataStatus;

    @Override
    public void dataAnalysis(String dataStr) {
        String[] values = dataStr.split(",", -1);
        values = super.valuesTrim(values);
        turningRate = values[1];
        dataStatus = values[2];

    }

    public String getTurningRate() {
        return turningRate;
    }

    public void setTurningRate(String turningRate) {
        this.turningRate = turningRate;
    }

    public String getDataStatus() {
        return dataStatus;
    }

    public void setDataStatus(String dataStatus) {
        this.dataStatus = dataStatus;
    }
}
