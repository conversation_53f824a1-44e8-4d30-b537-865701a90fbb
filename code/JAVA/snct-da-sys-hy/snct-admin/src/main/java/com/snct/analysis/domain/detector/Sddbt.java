package com.snct.analysis.domain.detector;

import com.snct.system.domain.msg.Instrument;

/**
 * @description: 探测仪的$SDDBT数据 传感器以下深度
 * Example:$SDDBT,2.0,f,0.6,M,0.3,F*01
 * $SDDBT,1,2,3,4,5,6
 * 逗号隔开
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class Sddbt extends Instrument {
    /**
     * 1.水深
     */
    private String depthF;
    /**
     * 2.单位 英尺
     */
    private String unitF;
    /**
     * 3.水深
     */
    private String depthM;
    /**
     * 4.单位 米
     */
    private String unitM;
    /**
     * 5.水深 英寻
     */
    private String depthT;
    /**
     * 6.单位+校验码
     */
    private String status;


    @Override
    public void dataAnalysis(String dataStr) {
        String[] values = dataStr.split(",", -1);
        values = super.valuesTrim(values);
        this.depthF = values[1];
        this.unitF = values[2];
        this.depthM = values[3];
        this.unitM = values[4];
        this.depthT = values[5];
        this.status = values[6];
    }

    public String getDepthF() {
        return depthF;
    }

    public void setDepthF(String depthF) {
        this.depthF = depthF;
    }

    public String getUnitF() {
        return unitF;
    }

    public void setUnitF(String unitF) {
        this.unitF = unitF;
    }

    public String getDepthM() {
        return depthM;
    }

    public void setDepthM(String depthM) {
        this.depthM = depthM;
    }

    public String getUnitM() {
        return unitM;
    }

    public void setUnitM(String unitM) {
        this.unitM = unitM;
    }

    public String getDepthT() {
        return depthT;
    }

    public void setDepthT(String depthT) {
        this.depthT = depthT;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
