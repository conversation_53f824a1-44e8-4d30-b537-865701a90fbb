package com.snct.analysis.domain.detector;


import com.snct.system.domain.msg.Instrument;

/**
 * @description: 探测仪的$SDDPT数据 传感器偏差变化
 * Example:$SDDPT,0.6,0.0,5.3*55
 * $SDDPT,<1>,<2>,5.3*55
 * 逗号隔开
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class Sddpt extends Instrument {
    /**
     * 1.水深
     */
    private String dptDepthM;
    /**
     * 2.探测仪偏移量  有正【传感器到水管】负【传感器到龙骨】
     */
    private String offset;
    /**
     * 3.校验码
     */
    private String status;

    @Override
    public void dataAnalysis(String dataStr) {
        String[] values = dataStr.split(",", -1);
        values = super.valuesTrim(values);
        dptDepthM = values[1];
        offset = values[2];
        status = values[3];
    }

    public String getDptDepthM() {
        return dptDepthM;
    }

    public void setDptDepthM(String dptDepthM) {
        this.dptDepthM = dptDepthM;
    }

    public String getOffset() {
        return offset;
    }

    public void setOffset(String offset) {
        this.offset = offset;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
