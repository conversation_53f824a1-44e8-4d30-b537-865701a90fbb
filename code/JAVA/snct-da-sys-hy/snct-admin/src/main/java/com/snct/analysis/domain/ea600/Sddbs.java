package com.snct.analysis.domain.ea600;


import com.snct.system.domain.msg.Instrument;

/**
 * @description: EA600的SDDBS数据  一秒一组
 * Example:$SDDBS,14103.37,f,4298.71,M,2350.56,F*34
 * $SDDBS, 1, 2, 3, 4, 5, F*hh<CR><LF>
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class Sddbs extends Instrument {
    /**
     * 1.water depth (in feet)
     */
    private String waterDepthF;
    /**
     * 2.水深单位 英尺
     */
    private String waterUnitF;
    /**
     * 3.water depth (in meters)
     */
    private String waterDepthM;
    /**
     * 4.水深单位 米
     */
    private String waterUnitM;
    /**
     * 5.water depth (in fathoms)
     */
    private String waterDepthI;
    /**
     * 6.单位+校验码
     */
    private String waterUnitI;

    @Override
    public void dataAnalysis(String dataStr) {
        String[] values = dataStr.split(",", -1);
        values = super.valuesTrim(values);
        this.waterDepthF = values[1];
        this.waterUnitF = values[2];
        this.waterDepthM = values[3];
        this.waterUnitM = values[4];
        this.waterDepthI = values[5];
        this.waterUnitI = values[6];
    }

    public String getWaterDepthF() {
        return waterDepthF;
    }

    public void setWaterDepthF(String waterDepthF) {
        this.waterDepthF = waterDepthF;
    }

    public String getWaterUnitF() {
        return waterUnitF;
    }

    public void setWaterUnitF(String waterUnitF) {
        this.waterUnitF = waterUnitF;
    }

    public String getWaterDepthM() {
        return waterDepthM;
    }

    public void setWaterDepthM(String waterDepthM) {
        this.waterDepthM = waterDepthM;
    }

    public String getWaterUnitM() {
        return waterUnitM;
    }

    public void setWaterUnitM(String waterUnitM) {
        this.waterUnitM = waterUnitM;
    }

    public String getWaterDepthI() {
        return waterDepthI;
    }

    public void setWaterDepthI(String waterDepthI) {
        this.waterDepthI = waterDepthI;
    }

    public String getWaterUnitI() {
        return waterUnitI;
    }

    public void setWaterUnitI(String waterUnitI) {
        this.waterUnitI = waterUnitI;
    }
}
