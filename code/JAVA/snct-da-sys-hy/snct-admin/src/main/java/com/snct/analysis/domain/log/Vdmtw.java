package com.snct.analysis.domain.log;


import com.snct.system.domain.msg.Instrument;

/**
 * @description: Log的$VDMTW数据 MTW水温
 * Example:$VDMTW,34.1,C*07
 * $VDVBW,<1>,C*07
 * 逗号隔开
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class Vdmtw extends Instrument {
    /**
     * 1.MTW水温
     */
    private String waterTemp;
    /**
     * 2.状态位 A=数据有效
     */
    private String status;


    @Override
    public void dataAnalysis(String dataStr) {
        String[] values = dataStr.split(",", -1);
        values = super.valuesTrim(values);
        waterTemp = values[1];
        status = values[2];
    }

    public String getWaterTemp() {
        return waterTemp;
    }

    public void setWaterTemp(String waterTemp) {
        this.waterTemp = waterTemp;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
