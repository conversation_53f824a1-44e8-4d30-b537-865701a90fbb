package com.snct.analysis.domain.log;

import com.snct.system.domain.msg.Instrument;

/**
 * @description: Log的$VDVHW数据  水流速和航向
 * Example:$VDVHW,,,,,0.0,N,0.0,K*5E
 * $VDVHW,,,,,<1>,N,<2>,K*5E
 * 逗号隔开
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class Vdvhw extends Instrument {
    /**
     * 1.节 船对水速度
     */
    private String waterSpeedN;

    /**
     * 2. 公里 船对水速度
     */
    private String waterSpeedK;
    /**
     * 3. 单位+校验码
     */
    private String  status;


    @Override
    public void dataAnalysis(String dataStr) {
        String[] values = dataStr.split(",");
        values = super.valuesTrim(values);
        waterSpeedN = values[5];
        waterSpeedK = values[7];
        status = values[8];
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getWaterSpeedN() {
        return waterSpeedN;
    }

    public void setWaterSpeedN(String waterSpeedN) {
        this.waterSpeedN = waterSpeedN;
    }

    public String getWaterSpeedK() {
        return waterSpeedK;
    }

    public void setWaterSpeedK(String waterSpeedK) {
        this.waterSpeedK = waterSpeedK;
    }
}
