package com.snct.analysis.domain.sbe21;

import com.snct.system.domain.msg.Instrument;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.util.Iterator;

/**
 * @description: sb21的scan数据 10秒一组
 * Example: <Sbe21Scan index='20938'><Field0>20939</Field0><Field1>29.96138</Field1></Sbe21Scan>
 * <Sbe21Scan index='(1)'><Field0>(2)</Field0><Field1>(3)</Field1><Field2>(4)</Field2><Field3>(5)</Field3><Field4>(6)</Field4><Field5>(7)</Field5><Field6>(8)</Field6></Sbe21Scan>
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class Scan extends Instrument {
    /**
     * 1. 数据标识0，注意这个字段外有单引号 ‘  ’
     */
    private String dataMark;
    /**
     * 2.累加数据标识1，数值上等于数据标识0加1
     */
    private String accumulatedData;
    /**
     * 3. SBE21温度
     */
    private String temperature;
    /**
     * 4.  原位温度
     */
    private String situTemperature;
    /**
     * 5. 盐度
     */
    private String salinity;
    /**
     * 6. 溶解氧
     */
    private String dissolvedOxygen;
    /**
     * 7. 叶绿素
     */
    private String chlorophyll;
    /**
     * 8. 浊度
     */
    private String turbidity;


    @Override
    public void dataAnalysis(String dataStr) {
        Document doc = null;
        String[] values = new String[7];
        try {
            doc = DocumentHelper.parseText(dataStr);
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        // 指向根节点
        Element root = doc.getRootElement();
        Iterator it = root.elementIterator();
        int count = 0;
        while (it.hasNext()) {
            // 一个Item节点
            Element element = (Element) it.next();
            values[count] = element.getTextTrim();
            count++;
        }
        values = super.valuesTrim(values);
        this.dataMark = String.valueOf(Integer.valueOf(values[0]) - 1);
        this.accumulatedData = values[0];
        this.temperature = values[1];
        this.situTemperature = values[2];
        this.salinity = values[3];
        this.dissolvedOxygen = values[4];
        this.chlorophyll = values[5];
        // 12.23出船 sbe21少一组数据
//        this.turbidity = Double.valueOf(values[6]);
    }

    public String getDataMark() {
        return dataMark;
    }

    public void setDataMark(String dataMark) {
        this.dataMark = dataMark;
    }

    public String getAccumulatedData() {
        return accumulatedData;
    }

    public void setAccumulatedData(String accumulatedData) {
        this.accumulatedData = accumulatedData;
    }

    public String getTemperature() {
        return temperature;
    }

    public void setTemperature(String temperature) {
        this.temperature = temperature;
    }

    public String getSituTemperature() {
        return situTemperature;
    }

    public void setSituTemperature(String situTemperature) {
        this.situTemperature = situTemperature;
    }

    public String getSalinity() {
        return salinity;
    }

    public void setSalinity(String salinity) {
        this.salinity = salinity;
    }

    public String getDissolvedOxygen() {
        return dissolvedOxygen;
    }

    public void setDissolvedOxygen(String dissolvedOxygen) {
        this.dissolvedOxygen = dissolvedOxygen;
    }

    public String getChlorophyll() {
        return chlorophyll;
    }

    public void setChlorophyll(String chlorophyll) {
        this.chlorophyll = chlorophyll;
    }

    public String getTurbidity() {
        return turbidity;
    }

    public void setTurbidity(String turbidity) {
        this.turbidity = turbidity;
    }
}
