package com.snct.analysis.domain.wind;


import com.snct.system.domain.msg.Instrument;

/**
 * @description: 风速风向仪的IIMWV数据
 * Example:[新]$IIMWV,223,R,009.47,N,A*1A
 * $WIMWV,1,<R>,2,M,3*<CS>
 * <AUTHOR>
 * @date 2025-04-11
 **/

public class Iimwv extends Instrument {
    /**
     * 1.相对风向
     */
    private String relativeWind;
    /**
     * 相对风向标识 参考值：R=相对值，T=理论值
     */
    private String windLogoR;
    /**
     * 2.相对风速
     */
    private String relativeWindSpeed;
    /**
     * 1.真实风向
     */
    private String trueWind;
    /**
     * 2.真实风速
     */
    private String trueWindSpeed;
    /**
     * 真实风向标识 参考值：R=相对值，T=理论值
     */
    private String windLogoT;
    /**
     * 风速单位 K=千米/小时  M=米/秒，N=海里/小时
     */
    private String windSpeedUnit;

    @Override
    public void dataAnalysis(String dataStr) {

        try {
            String[] values = dataStr.split(",", -1);
            values = super.valuesTrim(values);
            if ("R".equals(values[2])) {
                this.relativeWind = values[1];
                this.windLogoR = values[2];
                this.relativeWindSpeed = values[3];
            } else if ("T".equals(values[2])) {
                this.trueWind = values[1];
                this.windLogoT = values[2];
                this.trueWindSpeed = values[3];
            }
            this.windSpeedUnit = values[4];
        } catch (Exception e) {
            e.printStackTrace();

        }

    }

    public String getRelativeWind() {
        return relativeWind;
    }

    public void setRelativeWind(String relativeWind) {
        this.relativeWind = relativeWind;
    }

    public String getWindLogoR() {
        return windLogoR;
    }

    public void setWindLogoR(String windLogoR) {
        this.windLogoR = windLogoR;
    }

    public String getRelativeWindSpeed() {
        return relativeWindSpeed;
    }

    public void setRelativeWindSpeed(String relativeWindSpeed) {
        this.relativeWindSpeed = relativeWindSpeed;
    }

    public String getTrueWind() {
        return trueWind;
    }

    public void setTrueWind(String trueWind) {
        this.trueWind = trueWind;
    }

    public String getTrueWindSpeed() {
        return trueWindSpeed;
    }

    public void setTrueWindSpeed(String trueWindSpeed) {
        this.trueWindSpeed = trueWindSpeed;
    }

    public String getWindLogoT() {
        return windLogoT;
    }

    public void setWindLogoT(String windLogoT) {
        this.windLogoT = windLogoT;
    }

    public String getWindSpeedUnit() {
        return windSpeedUnit;
    }

    public void setWindSpeedUnit(String windSpeedUnit) {
        this.windSpeedUnit = windSpeedUnit;
    }
}
