package com.snct.analysis.vo;

import com.snct.common.annotation.Excel;

public class AwsHbaseVo {

    private String id;

    private String initialTime;
    @Excel(
        name = "录入时间"
    )
    private String initialBjTime;
    @Excel(
        name = "相对风向"
    )
    private String relativeWind;
    @Excel(
        name = "相对风向标识"
    )
    private String windLogoR;
    @Excel(
        name = "相对风速"
    )
    private String relativeWindSpeed;
    @Excel(
        name = "真实风向"
    )
    private String trueWind;
    @Excel(
        name = "真实风速"
    )
    private String trueWindSpeed;
    @Excel(
        name = "真实风向标识"
    )
    private String windLogoT;
    @Excel(
        name = "风速单位"
    )
    private String windSpeedUnit;
    @Excel(
        name = "传感器类型（气温）"
    )
    private String airTemType;
    @Excel(
        name = "气温值"
    )
    private String airTemperature;
    @Excel(
        name = "气温值单位（°C）"
    )
    private String airUnit;
    @Excel(
        name = "气温传感器ID"
    )
    private String airSensor;
    @Excel(
        name = "传感器类型（相对湿度）"
    )
    private String humidityType;
    @Excel(
        name = "相对湿度数值"
    )
    private String humidity;
    @Excel(
        name = "标识相对湿度的单位"
    )
    private String humidityUnit;
    @Excel(
        name = "相对湿度传感器ID"
    )
    private String humiditySensor;
    @Excel(
        name = "传感器类型"
    )
    private String pointTemType;
    @Excel(
        name = "露点温度数值"
    )
    private String pointTem;
    @Excel(
        name = "露点温度传感器ID"
    )
    private String pointTemSensor;
    @Excel(
        name = "传感器类型"
    )
    private String pressureType;
    @Excel(
        name = "气压数值"
    )
    private String pressure;
    @Excel(
        name = "气压传感器ID"
    )
    private String pressureSensor;
    private String latitude;
    private String longitude;

    public AwsHbaseVo() {
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRelativeWind() {
        return this.relativeWind;
    }

    public void setRelativeWind(String relativeWind) {
        this.relativeWind = relativeWind;
    }

    public String getWindLogoR() {
        return this.windLogoR;
    }

    public void setWindLogoR(String windLogoR) {
        this.windLogoR = windLogoR;
    }

    public String getRelativeWindSpeed() {
        return this.relativeWindSpeed;
    }

    public void setRelativeWindSpeed(String relativeWindSpeed) {
        this.relativeWindSpeed = relativeWindSpeed;
    }

    public String getTrueWind() {
        return this.trueWind;
    }

    public void setTrueWind(String trueWind) {
        this.trueWind = trueWind;
    }

    public String getTrueWindSpeed() {
        return this.trueWindSpeed;
    }

    public void setTrueWindSpeed(String trueWindSpeed) {
        this.trueWindSpeed = trueWindSpeed;
    }

    public String getWindLogoT() {
        return this.windLogoT;
    }

    public void setWindLogoT(String windLogoT) {
        this.windLogoT = windLogoT;
    }

    public String getAirTemperature() {
        return this.airTemperature;
    }

    public void setAirTemperature(String airTemperature) {
        this.airTemperature = airTemperature;
    }

    public String getHumidity() {
        return this.humidity;
    }

    public void setHumidity(String humidity) {
        this.humidity = humidity;
    }

    public String getInitialTime() {
        return this.initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return this.initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getPointTem() {
        return this.pointTem;
    }

    public void setPointTem(String pointTem) {
        this.pointTem = pointTem;
    }

    public String getPressure() {
        return this.pressure;
    }

    public void setPressure(String pressure) {
        this.pressure = pressure;
    }

    public String getLatitude() {
        return this.latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return this.longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getWindSpeedUnit() {
        return this.windSpeedUnit;
    }

    public void setWindSpeedUnit(String windSpeedUnit) {
        this.windSpeedUnit = windSpeedUnit;
    }

    public String getAirTemType() {
        return this.airTemType;
    }

    public void setAirTemType(String airTemType) {
        this.airTemType = airTemType;
    }

    public String getAirUnit() {
        return this.airUnit;
    }

    public void setAirUnit(String airUnit) {
        this.airUnit = airUnit;
    }

    public String getAirSensor() {
        return this.airSensor;
    }

    public void setAirSensor(String airSensor) {
        this.airSensor = airSensor;
    }

    public String getHumidityType() {
        return this.humidityType;
    }

    public void setHumidityType(String humidityType) {
        this.humidityType = humidityType;
    }

    public String getHumidityUnit() {
        return this.humidityUnit;
    }

    public void setHumidityUnit(String humidityUnit) {
        this.humidityUnit = humidityUnit;
    }

    public String getHumiditySensor() {
        return this.humiditySensor;
    }

    public void setHumiditySensor(String humiditySensor) {
        this.humiditySensor = humiditySensor;
    }

    public String getPointTemType() {
        return this.pointTemType;
    }

    public void setPointTemType(String pointTemType) {
        this.pointTemType = pointTemType;
    }

    public String getPointTemSensor() {
        return this.pointTemSensor;
    }

    public void setPointTemSensor(String pointTemSensor) {
        this.pointTemSensor = pointTemSensor;
    }

    public String getPressureType() {
        return this.pressureType;
    }

    public void setPressureType(String pressureType) {
        this.pressureType = pressureType;
    }

    public String getPressureSensor() {
        return this.pressureSensor;
    }

    public void setPressureSensor(String pressureSensor) {
        this.pressureSensor = pressureSensor;
    }
}