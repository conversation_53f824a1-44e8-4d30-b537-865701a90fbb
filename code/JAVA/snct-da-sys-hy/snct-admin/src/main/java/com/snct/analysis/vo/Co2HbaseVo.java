package com.snct.analysis.vo;


import com.snct.common.annotation.Excel;


/**
 * @description: co2  3分钟一组
 * 数据以空格隔开
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class Co2HbaseVo {

    private String id;
    /**
     * 录入时间
     */
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    private String initialBjTime;

//    ----------------- EQU ------------------------

    /**
     * 测量类型 kind, EQU=实际测量值，可能还有如ATM   STD1  STD4 ，先记录下来
     */
    @Excel(name="测量类型")
    private String measureType;
    /**
     * 2.错误代码 err 0=没有错误
     */
    @Excel(name="错误代码")
    private Integer errorCode;
    /**
     * 3. PC日期 PC_date  DD/MM/YY
     */
    @Excel(name="PC日期")
    private String pcDate;
    /**
     * 4. PC 时间 PC_time   HH:MM:SS
     */
    private String pcTime;
    /**
     * 平衡器温度   equ_temp
     */
    @Excel(name="平衡器温度")
    private String equTemp;
    /**
     * 6. 标准气标称值 std_val,没有数值为 NAN
     */
    @Excel(name="标准气标称值")
    private String stdVal;
    /**
     * CO2 检测值co2 (ppm),
     */
    @Excel(name="CO2")
    private String co2;
    /**
     * 8.校正后的CO2值co2_dry（ppm），  *用于参数显示
     */
    @Excel(name="校正后的CO2值")
    private String co2Dry;
    /**
     * CH4检测值 CH4(ppm)
     */
    @Excel(name="CH4检测值")
    private String ch4;
    /**
     * 10. 校正后的CH4值CH4_dry (ppm)，CH4_dry
     */
    @Excel(name="校正后的CH4值")
    private String ch4Dry;
    /**
     * 11. 水汽含量 H2O (%) ，
     */
    @Excel(name="水汽含量")
    private String h2o;
    /**
     * 12.  平衡器大气压力 equ_press（atm）
     */
    @Excel(name="平衡器大气压力")
    private String equPress;
    /**
     * 13.进入系统水流量H20_flow（L/min）
     */
    @Excel(name="进入系统水流量")
    private String h20Flow;
    /**
     * 14. 气路中气流量licor_flow (ml/min)
     */
    @Excel(name="气路中气流量")
    private String licorFlow;
    /**
     * 15. 平衡器气泵值 equ_pump
     */
    @Excel(name="平衡器气泵值")
    private String equPump;
    /**
     * 16. 预平衡器进气/出气流量vent_flow (ml/min)， 正值为进气，负值为出气
     * 进出气
     */
    @Excel(name="预平衡器进气/出气流量")
    private String inOutFlow;
    /**
     * 17.大气气路水汽值 atm_cond
     */
    @Excel(name="大气气路水汽值")
    private String atmCond;
    /**
     * 18.平衡器气路水汽值 equ_cond
     */
    @Excel(name="平衡器气路水汽值")
    private String equCond;
    /**
     * 19.  drip1
     */
    private String drip1;
    /**
     * 20. drip2
     */
    private String drip2;
    /**
     * 21.  cond_temp
     */
    private String condTemp;
    /**
     * 22.  干箱温度 dry_box_temp (℃)
     */
    @Excel(name="干箱温度")
    private String dryBoxTemp;
    /**
     * 23.溶氧值 Oxygen（μmol/L）
     */
    @Excel(name="溶氧值")
    private String oxygen;
    /**
     * 24.溶氧饱和度Saturation（%）
     */
    @Excel(name="溶氧饱和度")
    private String saturation;
    /**
     * 25. 溶氧传感器温度 Temperature（℃）
     */
    @Excel(name="溶氧传感器温度")
    private String temperature;
    /**
     * 26. SBE45传感器温度 SBE_Temp（℃）
     */
    @Excel(name="SBE45传感器温度")
    private String sbeTemp;
    /**
     * 27. SBE45电导率Conductivity
     */
    @Excel(name="SBE45电导率")
    private String conductivity;
    /**
     * 28. SBE45盐度 Salinity
     */
    @Excel(name="SBE45盐度")
    private String salinity;
    /**
     * 29. GPS日期 GPS_Date
     */
    @Excel(name="GPS日期")
    private String gpsDate;
    /**
     * 30.GPS时间 GPS_Time
     */
    @Excel(name="GPS时间")
    private String gpsTime;
    /**
     * 33.SBE21记录个数 TSG_count
     */
    @Excel(name="SBE21记录个数")
    private String tsgCount;
    /**
     * 34.  SBE21实验室温度传感器温度 TSG_Temp1（℃）
     */
    @Excel(name="SBE21实验室温度传感器温度")
    private String tsgTemp1;
    /**
     * SBE21原位传感器温度 TSG_temp2（℃）
     */
    @Excel(name="SBE21原位传感器温度")
    private String tsgTemp2;
    /**
     * 36. SBE21实验室盐度传感器值
     */
    @Excel(name="SBE21实验室盐度传感器值")
    private String sbeSalinity;
    /**
     * 37. SBE21 实验室溶氧值 DO （mg/L）
     */
    @Excel(name="实验室溶氧值")
    private String dissolvedOxygen;
    /**
     * SBE21 实验室叶绿素值 Chl (mg/m^3)
     */
    @Excel(name="实验室叶绿素值")
    private String chl;
    /**
     * 39.  SBE21 实验室浊度值 Turb（NTU）
     */
    @Excel(name="实验室浊度值")
    private String turb;
    /**
     * 40. 气温 Air_temp_degC（摄氏度）
     */
    @Excel(name="气温")
    private String airTemp;
    /**
     * 41. 湿度 Humidity_percent（%）
     */
    @Excel(name="湿度")
    private String humidity;
    /**
     * 42.  气压Atm_press_hPa(hPa)
     */
    @Excel(name="气压")
    private String atmPress;
    /**
     * 43.真风向True_wind_dir
     */
    @Excel(name="真风向")
    private String trueWindDir;
    /**
     * 真风速 True_wind_sp(m/s)
     */
    @Excel(name="真风速")
    private String trueWindSp;

//    ----------------- EQU ------------------------

    /**
     * 纬度
     */
    private String latitude;
    /**
     * 经度
     */
    private String longitude;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMeasureType() {
        return measureType;
    }

    public void setMeasureType(String measureType) {
        this.measureType = measureType;
    }

    public String getEquTemp() {
        return equTemp;
    }

    public void setEquTemp(String equTemp) {
        this.equTemp = equTemp;
    }

    public String getCo2() {
        return co2;
    }

    public void setCo2(String co2) {
        this.co2 = co2;
    }

    public String getSaturation() {
        return saturation;
    }

    public void setSaturation(String saturation) {
        this.saturation = saturation;
    }

    public String getTsgTemp2() {
        return tsgTemp2;
    }

    public void setTsgTemp2(String tsgTemp2) {
        this.tsgTemp2 = tsgTemp2;
    }

    public String getChl() {
        return chl;
    }

    public void setChl(String chl) {
        this.chl = chl;
    }

    public String getTrueWindSp() {
        return trueWindSp;
    }

    public void setTrueWindSp(String trueWindSp) {
        this.trueWindSp = trueWindSp;
    }

    public String getCh4() {
        return ch4;
    }

    public void setCh4(String ch4) {
        this.ch4 = ch4;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    public String getPcDate() {
        return pcDate;
    }

    public void setPcDate(String pcDate) {
        this.pcDate = pcDate;
    }

    public String getPcTime() {
        return pcTime;
    }

    public void setPcTime(String pcTime) {
        this.pcTime = pcTime;
    }

    public String getStdVal() {
        return stdVal;
    }

    public void setStdVal(String stdVal) {
        this.stdVal = stdVal;
    }

    public String getCo2Dry() {
        return co2Dry;
    }

    public void setCo2Dry(String co2Dry) {
        this.co2Dry = co2Dry;
    }

    public String getCh4Dry() {
        return ch4Dry;
    }

    public void setCh4Dry(String ch4Dry) {
        this.ch4Dry = ch4Dry;
    }

    public String getH2o() {
        return h2o;
    }

    public void setH2o(String h2o) {
        this.h2o = h2o;
    }

    public String getEquPress() {
        return equPress;
    }

    public void setEquPress(String equPress) {
        this.equPress = equPress;
    }

    public String getH20Flow() {
        return h20Flow;
    }

    public void setH20Flow(String h20Flow) {
        this.h20Flow = h20Flow;
    }

    public String getLicorFlow() {
        return licorFlow;
    }

    public void setLicorFlow(String licorFlow) {
        this.licorFlow = licorFlow;
    }

    public String getEquPump() {
        return equPump;
    }

    public void setEquPump(String equPump) {
        this.equPump = equPump;
    }

    public String getInOutFlow() {
        return inOutFlow;
    }

    public void setInOutFlow(String inOutFlow) {
        this.inOutFlow = inOutFlow;
    }

    public String getAtmCond() {
        return atmCond;
    }

    public void setAtmCond(String atmCond) {
        this.atmCond = atmCond;
    }

    public String getEquCond() {
        return equCond;
    }

    public void setEquCond(String equCond) {
        this.equCond = equCond;
    }

    public String getDrip1() {
        return drip1;
    }

    public void setDrip1(String drip1) {
        this.drip1 = drip1;
    }

    public String getDrip2() {
        return drip2;
    }

    public void setDrip2(String drip2) {
        this.drip2 = drip2;
    }

    public String getCondTemp() {
        return condTemp;
    }

    public void setCondTemp(String condTemp) {
        this.condTemp = condTemp;
    }

    public String getDryBoxTemp() {
        return dryBoxTemp;
    }

    public void setDryBoxTemp(String dryBoxTemp) {
        this.dryBoxTemp = dryBoxTemp;
    }

    public String getOxygen() {
        return oxygen;
    }

    public void setOxygen(String oxygen) {
        this.oxygen = oxygen;
    }

    public String getTemperature() {
        return temperature;
    }

    public void setTemperature(String temperature) {
        this.temperature = temperature;
    }

    public String getSbeTemp() {
        return sbeTemp;
    }

    public void setSbeTemp(String sbeTemp) {
        this.sbeTemp = sbeTemp;
    }

    public String getConductivity() {
        return conductivity;
    }

    public void setConductivity(String conductivity) {
        this.conductivity = conductivity;
    }

    public String getSalinity() {
        return salinity;
    }

    public void setSalinity(String salinity) {
        this.salinity = salinity;
    }

    public String getGpsDate() {
        return gpsDate;
    }

    public void setGpsDate(String gpsDate) {
        this.gpsDate = gpsDate;
    }

    public String getGpsTime() {
        return gpsTime;
    }

    public void setGpsTime(String gpsTime) {
        this.gpsTime = gpsTime;
    }

    public String getTsgCount() {
        return tsgCount;
    }

    public void setTsgCount(String tsgCount) {
        this.tsgCount = tsgCount;
    }

    public String getTsgTemp1() {
        return tsgTemp1;
    }

    public void setTsgTemp1(String tsgTemp1) {
        this.tsgTemp1 = tsgTemp1;
    }

    public String getSbeSalinity() {
        return sbeSalinity;
    }

    public void setSbeSalinity(String sbeSalinity) {
        this.sbeSalinity = sbeSalinity;
    }

    public String getDissolvedOxygen() {
        return dissolvedOxygen;
    }

    public void setDissolvedOxygen(String dissolvedOxygen) {
        this.dissolvedOxygen = dissolvedOxygen;
    }

    public String getTurb() {
        return turb;
    }

    public void setTurb(String turb) {
        this.turb = turb;
    }

    public String getAirTemp() {
        return airTemp;
    }

    public void setAirTemp(String airTemp) {
        this.airTemp = airTemp;
    }

    public String getHumidity() {
        return humidity;
    }

    public void setHumidity(String humidity) {
        this.humidity = humidity;
    }

    public String getAtmPress() {
        return atmPress;
    }

    public void setAtmPress(String atmPress) {
        this.atmPress = atmPress;
    }

    public String getTrueWindDir() {
        return trueWindDir;
    }

    public void setTrueWindDir(String trueWindDir) {
        this.trueWindDir = trueWindDir;
    }
}
