package com.snct.analysis.vo;


import com.snct.common.annotation.Excel;
import com.snct.common.annotation.Excel;

/**
 * @description:  探测仪数据
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class DetectorHbaseVo {

    private String id;
    /**
     * 录入时间
     */
    private String initialTime;
    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    private String initialBjTime;

//  ---------------------- SDDBT ---------------------------
    /**
     * 1.水深
     */
    @Excel(name="水深")
    private String depthF;
    /**
     * 2.单位 英尺
     */
    @Excel(name="单位 英尺")
    private String unitF;
    /**
     * 3.水深
     */
    @Excel(name="水深")
    private String depthM;
    /**
     * 4.单位 米
     */
    @Excel(name="单位 米")
    private String unitM;
//  ---------------------- SDDBT ---------------------------


//  ---------------------- SDDPT ---------------------------
    /**
     * 2.探测仪偏移量  有正【传感器到水管】负【传感器到龙骨】
     */
    @Excel(name="探测仪偏移量")
    private String offset;
//  ---------------------- SDDPT ---------------------------

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDepthM() {
        return depthM;
    }

    public void setDepthM(String depthM) {
        this.depthM = depthM;
    }

    public String getOffset() {
        return offset;
    }

    public void setOffset(String offset) {
        this.offset = offset;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }
}
