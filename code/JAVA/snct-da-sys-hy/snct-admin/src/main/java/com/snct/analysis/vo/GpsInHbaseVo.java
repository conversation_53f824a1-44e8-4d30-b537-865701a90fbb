package com.snct.analysis.vo;

public class GpsInHbaseVo {
    private String id;
    /**
     * 录入时间
     */
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    private String initialBjTime;

    //-----加速度输出---
    /**
     * 加速度X
     */
    private String axSpead;

    /**
     * 加速度Y
     */
    private String aySpead;

    /**
     * 加速度Z
     */
    private String azSpead;

    /**
     * 加速度-温度
     */
    private String tlSpead;

    //-----角速度输出---
    /**
     * 角速度X
     */
    private String wxSpead;

    /**
     * 角速度Y
     */
    private String wySpead;

    /**
     * 角速度Z
     */
    private String wzSpead;

    /**
     * 角速度-电压
     */
    private String voSpead;

    //-----角度输出---
    /**
     * 滚转角X
     */
    private String rollSpead;

    /**
     * 俯仰角Y
     */
    private String pitchRoll;

    /**
     * 偏航角Z
     */
    private String yawRoll;

    /**
     * 版本号
     */
    private String versionRoll;

    //-----经纬度输出---
    /**
     * 纬度
     */
    private String latitude;
    /**
     * 经度
     */
    private String longitude;

    //-----GPS数据输出---
    /**
     * GPS海拔
     */
    private String gpsHeight;
    /**
     * GPS航向
     */
    private String gpsYaw;
    /**
     * GPS地速
     */
    private String groundRate;

    //-----GPS定位精度输出---
    /**
     * 卫星数
     */
    private String snl;

    /**
     * 位置定位精度
     */
    private String definition;

    /**
     * 水平定位精度
     */
    private String gnsDefinition;

    /**
     * 垂直定位精度
     */
    private String verDefinition;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getAxSpead() {
        return axSpead;
    }

    public void setAxSpead(String axSpead) {
        this.axSpead = axSpead;
    }

    public String getAySpead() {
        return aySpead;
    }

    public void setAySpead(String aySpead) {
        this.aySpead = aySpead;
    }

    public String getAzSpead() {
        return azSpead;
    }

    public void setAzSpead(String azSpead) {
        this.azSpead = azSpead;
    }

    public String getTlSpead() {
        return tlSpead;
    }

    public void setTlSpead(String tlSpead) {
        this.tlSpead = tlSpead;
    }

    public String getWxSpead() {
        return wxSpead;
    }

    public void setWxSpead(String wxSpead) {
        this.wxSpead = wxSpead;
    }

    public String getWySpead() {
        return wySpead;
    }

    public void setWySpead(String wySpead) {
        this.wySpead = wySpead;
    }

    public String getWzSpead() {
        return wzSpead;
    }

    public void setWzSpead(String wzSpead) {
        this.wzSpead = wzSpead;
    }

    public String getVoSpead() {
        return voSpead;
    }

    public void setVoSpead(String voSpead) {
        this.voSpead = voSpead;
    }

    public String getRollSpead() {
        return rollSpead;
    }

    public void setRollSpead(String rollSpead) {
        this.rollSpead = rollSpead;
    }

    public String getPitchRoll() {
        return pitchRoll;
    }

    public void setPitchRoll(String pitchRoll) {
        this.pitchRoll = pitchRoll;
    }

    public String getYawRoll() {
        return yawRoll;
    }

    public void setYawRoll(String yawRoll) {
        this.yawRoll = yawRoll;
    }

    public String getVersionRoll() {
        return versionRoll;
    }

    public void setVersionRoll(String versionRoll) {
        this.versionRoll = versionRoll;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getGpsHeight() {
        return gpsHeight;
    }

    public void setGpsHeight(String gpsHeight) {
        this.gpsHeight = gpsHeight;
    }

    public String getGpsYaw() {
        return gpsYaw;
    }

    public void setGpsYaw(String gpsYaw) {
        this.gpsYaw = gpsYaw;
    }

    public String getGroundRate() {
        return groundRate;
    }

    public void setGroundRate(String groundRate) {
        this.groundRate = groundRate;
    }

    public String getSnl() {
        return snl;
    }

    public void setSnl(String snl) {
        this.snl = snl;
    }

    public String getDefinition() {
        return definition;
    }

    public void setDefinition(String definition) {
        this.definition = definition;
    }

    public String getGnsDefinition() {
        return gnsDefinition;
    }

    public void setGnsDefinition(String gnsDefinition) {
        this.gnsDefinition = gnsDefinition;
    }

    public String getVerDefinition() {
        return verDefinition;
    }

    public void setVerDefinition(String verDefinition) {
        this.verDefinition = verDefinition;
    }
}
