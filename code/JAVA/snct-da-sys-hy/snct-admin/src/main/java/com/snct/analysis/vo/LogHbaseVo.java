package com.snct.analysis.vo;


import com.snct.common.annotation.Excel;
import com.snct.common.annotation.Excel;

/**
 * @description:  Log数据 1秒一组
 * <AUTHOR>
 * @date 2025-04-11
 **/
public class LogHbaseVo {

    private String id;
    /**
     * 录入时间
     */
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    private String initialBjTime;

    /**
     * 1.船舶航行总里程
     */
    @Excel(name="船舶航行总里程")
    private String totalShipMileage;

    /**
     * 1.对水速度
     */
    @Excel(name="对水速度")
    private String waterSpeed;

    /**
     * 3.mtw水温
     */
    @Excel(name="mtw水温")
    private String waterTemperature;

    /**
     * 1.节 船对水速度
     */
    @Excel(name="船对水速度(节)")
    private String waterSpeedN;

    /**
     * 2. 公里 船对水速度
     */
    @Excel(name="船对水速度(公里)")
    private String waterSpeedK;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getTotalShipMileage() {
        return totalShipMileage;
    }

    public void setTotalShipMileage(String totalShipMileage) {
        this.totalShipMileage = totalShipMileage;
    }

    public String getWaterSpeed() {
        return waterSpeed;
    }

    public void setWaterSpeed(String waterSpeed) {
        this.waterSpeed = waterSpeed;
    }

    public String getWaterTemperature() {
        return waterTemperature;
    }

    public void setWaterTemperature(String waterTemperature) {
        this.waterTemperature = waterTemperature;
    }

    public String getWaterSpeedN() {
        return waterSpeedN;
    }

    public void setWaterSpeedN(String waterSpeedN) {
        this.waterSpeedN = waterSpeedN;
    }

    public String getWaterSpeedK() {
        return waterSpeedK;
    }

    public void setWaterSpeedK(String waterSpeedK) {
        this.waterSpeedK = waterSpeedK;
    }
}
