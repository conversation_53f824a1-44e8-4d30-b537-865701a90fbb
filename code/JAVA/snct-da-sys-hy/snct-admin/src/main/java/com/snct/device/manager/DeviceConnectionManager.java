package com.snct.device.manager;

import com.snct.common.core.redis.RedisCache;
import com.snct.system.domain.Device;
import com.snct.system.service.IDeviceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 设备连接状态管理器
 * 基于Redis Key过期机制判断设备连接状态
 * 
 * <AUTHOR>
 */
@Component
public class DeviceConnectionManager {
    
    private static final Logger logger = LoggerFactory.getLogger(DeviceConnectionManager.class);
    
    @Autowired
    private RedisCache redisCache;
    
    @Autowired
    private IDeviceService deviceService;
    
    // 设备活跃状态缓存前缀
    private static final String DEVICE_ACTIVE_PREFIX = "device_active:";
    
    /**
     * 连接状态枚举
     */
    public enum ConnectionStatus {
        CONNECTED(1L, "连接"),
        DISCONNECTED(0L, "断开"),
        UNKNOWN(-1L, "未知");
        
        private final Long value;
        private final String description;
        
        ConnectionStatus(Long value, String description) {
            this.value = value;
            this.description = description;
        }
        
        public Long getValue() { 
            return value; 
        }
        
        public String getDescription() { 
            return description; 
        }
    }
    
    /**
     * 协议类型枚举
     */
    public enum ProtocolType {
        SNMP("SNMP协议", 300),    // 5分钟
        TCP("TCP协议", 180),     // 3分钟
        SERIAL("串口协议", 30),  // 30秒
        HTTP("HTTP协议", 600);   // 10分钟
        
        private final String description;
        private final int timeoutSeconds;
        
        ProtocolType(String description, int timeoutSeconds) {
            this.description = description;
            this.timeoutSeconds = timeoutSeconds;
        }
        
        public String getDescription() { 
            return description; 
        }
        
        public int getTimeoutSeconds() {
            return timeoutSeconds;
        }
    }
    
    /**
     * 标记设备为活跃状态（有数据输入时调用）
     * 
     * @param deviceCode 设备编码
     * @param protocol 协议类型
     */
    public void markDeviceActive(String deviceCode, ProtocolType protocol) {
        try {
            String activeKey = DEVICE_ACTIVE_PREFIX + deviceCode;
            int timeoutSeconds = protocol.getTimeoutSeconds();
            
            // 设置Redis key，过期时间为协议对应的超时时间
            redisCache.setCacheObject(activeKey, "1", timeoutSeconds, TimeUnit.SECONDS);
            
            logger.debug("标记设备[{}]为活跃状态，超时时间: {}秒", deviceCode, timeoutSeconds);
            
        } catch (Exception e) {
            logger.error("标记设备[{}]活跃状态失败", deviceCode, e);
        }
    }
    
    /**
     * 检查设备是否活跃（基于Redis key是否存在）
     * 
     * @param deviceCode 设备编码
     * @return true=活跃，false=不活跃
     */
    public boolean isDeviceActive(String deviceCode) {
        try {
            String activeKey = DEVICE_ACTIVE_PREFIX + deviceCode;
            String value = redisCache.getCacheObject(activeKey);
            return "1".equals(value);
        } catch (Exception e) {
            logger.error("检查设备[{}]活跃状态失败", deviceCode, e);
            return false;
        }
    }
    
    /**
     * 更新设备连接状态
     * 
     * @param device 设备对象
     * @param status 连接状态
     * @param protocol 协议类型
     */
    public void updateDeviceConnectionStatus(Device device, ConnectionStatus status, ProtocolType protocol) {
        if (device == null) {
            return;
        }
        
        try {
            // 如果是连接状态，标记设备为活跃
            if (status == ConnectionStatus.CONNECTED) {
                markDeviceActive(device.getCode(), protocol);
            }
            
            // 获取当前数据库中的状态
            Long currentStatus = device.getConnectStatus();
            Long newStatus = status.getValue();
            
            // 只有状态发生变化时才更新数据库
            if (!Objects.equals(currentStatus, newStatus)) {
                device.setConnectStatus(newStatus);
                deviceService.updateDevice(device);
                
                logger.info("设备[{}]连接状态变更: {} -> {}, 协议: {}", 
                           device.getCode(), 
                           getStatusDescription(currentStatus), 
                           status.getDescription(), 
                           protocol.getDescription());
            }
        } catch (Exception e) {
            logger.error("更新设备[{}]连接状态失败", device.getCode(), e);
        }
    }
    
    /**
     * 检查设备连接状态并更新数据库
     * 
     * @param devices 设备列表
     * @param protocol 协议类型
     */
    public void checkAndUpdateDeviceStatus(List<Device> devices, ProtocolType protocol) {
        if (devices == null || devices.isEmpty()) {
            return;
        }
        
        for (Device device : devices) {
            try {
                boolean isActive = isDeviceActive(device.getCode());
                ConnectionStatus targetStatus = isActive ? ConnectionStatus.CONNECTED : ConnectionStatus.DISCONNECTED;
                
                // 只有当前状态与目标状态不一致时才更新
                Long currentStatus = device.getConnectStatus();
                if (!Objects.equals(currentStatus, targetStatus.getValue())) {
                    device.setConnectStatus(targetStatus.getValue());
                    deviceService.updateDevice(device);
                    
                    //logger.info("设备[{}]连接状态检测更新: {} -> {}, 协议: {}",
                    //           device.getCode(),
                    //           getStatusDescription(currentStatus),
                    //           targetStatus.getDescription(),
                    //           protocol.getDescription());
                }
            } catch (Exception e) {
                logger.error("检查设备[{}]连接状态异常", device.getCode(), e);
            }
        }
    }
    
    /**
     * 获取状态描述
     */
    private String getStatusDescription(Long status) {
        if (status == null) {
            return "未知";
        }
        
        for (ConnectionStatus connectionStatus : ConnectionStatus.values()) {
            if (connectionStatus.getValue().equals(status)) {
                return connectionStatus.getDescription();
            }
        }
        return "未知";
    }
}
