package com.snct.device.service;

import com.snct.serialport.SerialPortUtil;
import com.snct.system.domain.Device;
import com.snct.system.domain.SerialConfig;
import com.snct.system.service.IDeviceService;
import com.snct.system.service.ISerialConfigService;
import gnu.io.SerialPort;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 功放设备服务
 * 负责功放设备的通信和数据处理
 * 
 * <AUTHOR>
 */
@Service
public class AmplifierDeviceService {
    
    private static final Logger logger = LoggerFactory.getLogger(AmplifierDeviceService.class);
    
    // 功放设备类型ID
    private static final Long AMPLIFIER_DEVICE_TYPE = 53L;
    
    // 功放查询命令
    private static final String QUERY_COMMAND = "1603A255F7";
    private static final String RESPONSE_COMMAND = "110351AAFB";
    
    @Autowired
    private IDeviceService deviceService;
    
    @Autowired
    private ISerialConfigService serialConfigService;
    
    /**
     * 查询所有功放设备数据
     * 向所有可用的功放设备发送查询命令
     */
    public void queryAllAmplifierData() {
        logger.debug("执行功放设备查询任务");
        
        // 从数据库中获取type为53的功放设备
        Device queryDevice = new Device();
        queryDevice.setType(AMPLIFIER_DEVICE_TYPE);
        queryDevice.setEnable(1);
        queryDevice.setConnectType(1); // 串口连接
        List<Device> amplifierDevices = deviceService.selectDeviceList(queryDevice);
        
        if (amplifierDevices == null || amplifierDevices.isEmpty()) {
            logger.info("未找到可用的功放设备，跳过查询任务");
            return;
        }
        
        logger.debug("找到{}个功放设备需要查询", amplifierDevices.size());
        
        // 对每个设备执行查询
        for (Device amplifierDevice : amplifierDevices) {
            queryAmplifierDevice(amplifierDevice);
        }
    }
    
    /**
     * 查询单个功放设备
     * 
     * @param device 功放设备对象
     * @return 是否成功发送查询命令
     */
    public boolean queryAmplifierDevice(Device device) {
        if (device == null) {
            logger.error("设备对象为空，无法查询功放设备");
            return false;
        }
        
        try {
            // 获取旧串口名称（硬件实际串口）
            String oldSerialPort = getOldSerialPort(device.getSerialPort());
            
            if (StringUtils.isEmpty(oldSerialPort)) {
                logger.error("设备[{}]未找到对应的旧串口名称", device.getCode());
                return false;
            }
            
            // 获取已打开的串口
            SerialPort serialPort = SerialPortUtil.getOpenPortByName(oldSerialPort);
            
            if (serialPort != null) {
                // 发送查询命令
                SerialPortUtil.sendCmd(QUERY_COMMAND, serialPort);
                logger.info("功放设备[{}]已发送查询命令", device.getCode());
                return true;
            } else {
                logger.warn("功放设备[{}]的串口未打开或不可用，尝试重新打开", device.getCode());
                
                // 尝试打开串口
                SerialPort newSerialPort = SerialPortUtil.openComPort(oldSerialPort, 
                    device.getBaudRate(), 
                    device.getDataBits(), 
                    device.getStopBits(), 
                    device.getParity());
                
                if (newSerialPort != null) {
                    logger.info("成功打开功放设备[{}]的串口", device.getCode());
                    SerialPortUtil.addOpenPort(device.getSerialPort(), newSerialPort);
                    
                    // 发送查询命令
                    SerialPortUtil.sendCmd(QUERY_COMMAND, newSerialPort);
                    logger.info("功放设备[{}]已发送查询命令", device.getCode());
                    return true;
                } else {
                    logger.error("无法打开功放设备[{}]的串口", device.getCode());
                    return false;
                }
            }
        } catch (Exception e) {
            logger.error("查询功放设备[{}]异常", device.getCode(), e);
            return false;
        }
    }
    
    /**
     * 获取旧串口名称
     * 根据新串口名称从数据库查询对应的旧串口名称
     * 
     * @param newSerialPort 新串口名称
     * @return 对应的旧串口名称，如果未找到则返回null
     */
    private String getOldSerialPort(String newSerialPort) {
        if (StringUtils.isNotBlank(newSerialPort)) {
            // 构建查询条件
            SerialConfig se = new SerialConfig();
            se.setNewName(newSerialPort);
            
            // 查询串口配置
            SerialConfig serialConfig = serialConfigService.selectSerialConfig(se);
            
            // 如果找到串口配置，返回旧的串口名称
            if (serialConfig != null) {
                return serialConfig.getOldName();
            }
        }
        return null;
    }
}
