package com.snct.serialport;

import com.alibaba.fastjson2.JSONObject;
import com.snct.analysis.BaseAnalysis;
import com.snct.common.constant.CacheConstants;
import com.snct.common.core.redis.RedisCache;
import com.snct.common.utils.DateUtils;
import com.snct.common.utils.spring.SpringUtils;
import com.snct.device.domain.Amplifier;
import com.snct.kafka.KafkaMessage;
import com.snct.kafka.KafkaService;
import com.snct.system.domain.Device;
import com.snct.system.domain.SerialConfig;
import com.snct.system.domain.msg.BuMsgGps;
import com.snct.system.domain.msg.aws.BuMsgAws;
import com.snct.system.service.IDefaultAttributeService;
import com.snct.system.service.IDeviceService;
import com.snct.system.service.ISerialConfigService;
import com.snct.system.service.impl.SerialConfigServiceImpl;
import com.snct.utils.HexUtil;
import com.snct.web.controller.business.DeviceController;
import gnu.io.SerialPort;
import gnu.io.SerialPortEvent;
import gnu.io.SerialPortEventListener;
import net.sf.marineapi.nmea.sentence.Sentence;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 串口通信服务类
 * 本服务负责管理多个串口设备的通信
 *
 * <AUTHOR>
 */
@Service
public class RtxtService {
    private static final Logger logger = LoggerFactory.getLogger(RtxtService.class);

    @Autowired
    private IDeviceService deviceService;

    @Autowired
    private IDefaultAttributeService defaultAttributeService;

    @Autowired
    public ClientListernerUtil clientListernerUtil;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ISerialConfigService serialConfigService;

    @Autowired
    private KafkaService kafkaService;

    // 北斗发送相关Redis键名
    private static final String BD2_TIME = CacheConstants.BD_SEND_TIME +"bd2";
    private static final String BD3_1_TIME = CacheConstants.BD_SEND_TIME +"bd3_1";
    private static final String BD3_2_TIME = CacheConstants.BD_SEND_TIME +"bd3_2";
    private static final String BD2_HISTORY = CacheConstants.DEVICE_HISTORY_DATA_KEY+"bd2";
    private static final String BD3_1_HISTORY = CacheConstants.DEVICE_HISTORY_DATA_KEY+"bd3_1";
    private static final String BD3_2_HISTORY = CacheConstants.DEVICE_HISTORY_DATA_KEY+"bd3_2";

    // 设备数据缓存键名
    private static final String GPS_DATA = CacheConstants.DEVICE_DATA_KEY + "gps";
    private static final String AWS_DATA = CacheConstants.DEVICE_DATA_KEY + "aws";
    private static final String AMPLIFIER_DATA = CacheConstants.DEVICE_DATA_KEY + "amplifier";
    private static final String MODEM_DATA = CacheConstants.DEVICE_DATA_KEY + "modem";
    private static final String PDU_DATA = CacheConstants.DEVICE_DATA_KEY + "pdu";
    private static final String ATTITUDE_DATA = CacheConstants.DEVICE_DATA_KEY + "attitude";

    // 消息长度限制
    private static final int BD2_MSG_LIMIT = 69;      // 北斗2消息长度限制(69)
    private static final int BD3_MSG_LIMIT = 220;     // 北斗3消息长度限制(450)

//    // 发送间隔(毫秒)
//    private static final long BD2_SEND_INTERVAL = 80080; // 121秒
//    private static final long BD3_SEND_INTERVAL = 70070; // 121秒
    private static final long BD2_SEND_INTERVAL = 125000; // 121秒
    private static final long BD3_SEND_INTERVAL = 125000; // 121秒

    // 判断当前系统类型
    private static final boolean IS_WINDOWS = System.getProperty("os.name").toLowerCase().contains("win");

    // 串口名称映射(Linux->Windows)
    private static final String PORT_GPS_BD2 = IS_WINDOWS ? "COM1" : "/dev/ttyS0";         // 北斗2
    private static final String PORT_AWS = IS_WINDOWS ? "COM2" : "/dev/ttyS1";             // 气象站
    private static final String PORT_AMPLIFIER = IS_WINDOWS ? "COM3" : "/dev/ttyS2";       // 功放
    private static final String PORT_BD3_1 = IS_WINDOWS ? "COM4" : "/dev/ttyS3";           // 北斗3-1
    private static final String PORT_BD3_2 = IS_WINDOWS ? "COM5" : "/dev/ttyS4";           // 北斗3-2

    /**
     * 初始化并处理所有串口服务
     * 1. 清除Redis中的时间缓存
     * 2. 查找系统所有串口
     * 3. 清除已打开的串口映射
     * 4. 为每个串口设置监听器
     */
    public void handleAllService() {
        // 清除Redis中的时间缓存
        this.redisCache.deleteObject(BD2_TIME);
        this.redisCache.deleteObject(BD3_1_TIME);
        this.redisCache.deleteObject(BD3_2_TIME);

        // 查找系统所有串口
        SerialPortUtil.findSystemAllSerialPort();

        // 清除已打开的串口映射
        SerialPortUtil.cleanOpenMap();

        // 获取所有串口名称集合
        Set<String> strings = SerialPortUtil.getSerialPortNameSets();

        // 为每个串口设置监听器
        for (String serialName : strings) {
            toListenerSerialPort(serialName);
        }
    }

    /**
     * 根据新的串口名称获取对应的旧串口名称
     *
     * @param newSerialPort 新的串口名称
     * @return 对应的旧串口名称，如果没有找到则返回null
     */
    public static String getOldSerialPort(String newSerialPort) {
        ISerialConfigService serialConfigService = SpringUtils.getBean(SerialConfigServiceImpl.class);
        if (StringUtils.isNotBlank(newSerialPort)) {
            SerialConfig se = new SerialConfig();
            se.setNewName(newSerialPort);
            SerialConfig serialConfig = serialConfigService.selectSerialConfig(se);
            if (serialConfig != null) {
                String oldSerialName = serialConfig.getOldName();
                return oldSerialName;
            }
            return null;
        }
        return null;
    }

    public Integer toListenerSerialPort(Device deviceEntity) {
        return 1;
    }

    /**
     * 为指定串口设置监听器
     *
     * @param serialName 串口名称
     * @return 操作结果：1-成功，0-失败
     *
     * 核心功能：
     * 1. 根据不同串口设置不同波特率
     * 2. 打开串口并添加到已打开串口映射
     * 3. 为串口设置事件监听器处理数据接收
     * 4. 对不同设备接收的数据进行相应处理
     */
    public synchronized Integer toListenerSerialPort(final String serialName) {
        if (StringUtils.isEmpty(serialName)) {
            return 0;
        }
        // 设置串口波特率
        // 不同设备需要不同的串口参数：
        // - 气象站(/dev/ttyS1 或 COM2)：4800波特率(默认)
        // - GPS设备(/dev/ttyS0 或 COM1)：19200波特率
        // - 北斗3-1接收机(/dev/ttyS3 或 COM4)：19200波特率
        // - 北斗3-2接收机(/dev/ttyS4 或 COM5)：19200波特率
        // - 放大器(/dev/ttyS2 或 COM3)：9600波特率
        int b = 4800;
        if (!serialName.equalsIgnoreCase(PORT_AWS)) {
            if (serialName.equalsIgnoreCase(PORT_GPS_BD2)) {
                b = 19200;
            } else if (serialName.equalsIgnoreCase(PORT_BD3_1)) {
                //b = 115200;
                b = 19200;
            } else if (serialName.equalsIgnoreCase(PORT_BD3_2)) {
                //b = 115200;
                b = 19200;
            } else if (serialName.equalsIgnoreCase(PORT_AMPLIFIER)) {
                b = 9600;
            }
        }
        // 打开串口
        final SerialPort serialPort = SerialPortUtil.openComPort(serialName, b, 8, 1, 0);
        if (serialPort == null) {
            return 0;
        }
        SerialPortUtil.addOpenPort(serialName, serialPort);

        // 为串口设置事件监听器
        SerialPortUtil.setListenerToSerialPort(serialPort, new SerialPortEventListener() {
            public void serialEvent(SerialPortEvent arg0) {
                // 当接收到数据时(EVENT_DATA_AVAILABLE事件，值为1)
                if (arg0.getEventType() == SerialPortEvent.DATA_AVAILABLE) {
                    // 获取各设备的时间缓存(用于控制设备消息发送间隔)
                    // 不同设备有不同的时间控制缓存键，对应不同的发送间隔：
                    String bd2_time = RtxtService.this.redisCache.getCacheObject(BD2_TIME);
                    String bd31_time = RtxtService.this.redisCache.getCacheObject(BD3_1_TIME);
                    String bd32_time = RtxtService.this.redisCache.getCacheObject(BD3_2_TIME);

                    // 初始化时间缓存(如果为空)
                    // fetchWholeSecond方法获取当前时间戳的整秒值
                    if (!StringUtils.isNotBlank(bd2_time)) {
                        RtxtService.this.redisCache.setCacheObject(BD2_TIME, DateUtils.fetchWholeSecond(System.currentTimeMillis()).toString());
                        //RtxtService.this.redisCache.setCacheObject(REDIS_KEY_BD2_TIME, "0");
                    }
                    if (!StringUtils.isNotBlank(bd31_time)) {
                        RtxtService.this.redisCache.setCacheObject(BD3_1_TIME, DateUtils.fetchWholeSecond(System.currentTimeMillis()).toString());
                        //RtxtService.this.redisCache.setCacheObject(REDIS_KEY_BD3_1_TIME, "0");
                    }
                    if (!StringUtils.isNotBlank(bd32_time)) {
                        RtxtService.this.redisCache.setCacheObject(BD3_2_TIME, DateUtils.fetchWholeSecond(System.currentTimeMillis()).toString());
                        //RtxtService.this.redisCache.setCacheObject(REDIS_KEY_BD3_2_TIME, "0");
                    }

                    // 读取串口数据(最多读取20字节)
                    byte[] bytes = SerialPortUtil.readData(serialPort, 20);
                    Long time = System.currentTimeMillis();

                    // 获取设备状态缓存
                    // aws和gps对象分别存储了气象站和GPS设备的最新状态数据
                    Object resultAws = RtxtService.this.redisCache.getCacheObject(AWS_DATA);
                    Object resultGps = RtxtService.this.redisCache.getCacheObject(GPS_DATA);

                    // 处理/dev/ttyS1串口数据(气象站设备)
                    if (serialName.equalsIgnoreCase(PORT_AWS)) {
                        // 设置设备活跃状态缓存，有效期30秒
                        RtxtService.this.redisCache.setCacheObject("CJ_CK_COM2", "1", 30, TimeUnit.SECONDS);
                        List<String> strs = new ArrayList<>();
                        String msg = new String(bytes, StandardCharsets.UTF_8);
                        logger.info("----aws-:-" + msg );
                        // 查找气象站设备
                        Device aws = findDeviceByType(38);
                        // 保存预览数据
                        savePreviewData(aws, msg);

                        // 处理气象站NMEA格式数据($WIMWV和$WIXDR)
                        if (msg.contains("$WIMWV") || msg.contains("$WIXDR")) {
                            // 如果同时包含两种数据，将它们分开处理
                            if (msg.contains("$WIMWV") && msg.contains("$WIXDR")) {
                                // 清理可能的换行符和制表符
                                if (msg.contains("\n\t")) {
                                    msg = msg.replace("\n", "").replace("\t", "");
                                }
                                // 分割两种数据
                                strs.add(msg.substring(msg.indexOf("$WIMWV"), msg.indexOf("$WIXDR")));
                                strs.add(msg.substring(msg.indexOf("$WIXDR"), msg.length()));
                            } else {
                                // 只包含一种数据
                                strs.add(msg);
                            }

                            // 逐条处理数据
                            for (int i = 0; i < strs.size(); i++) {
                                // 处理气象站数据
                                KafkaMessage receiveMessage = RtxtService.getReceiveMessage(serialName, strs.get(i), time, 38, "038A");
                                //if (ClientListernerUtil.states == 0) {
                                //    WebSocketServer.sendRealTimeData(strs.get(i), "COM2");
                                //}
                                Object hbaseVo = BaseAnalysis.analysisData(receiveMessage, 1, resultAws);
                                if (hbaseVo != null) {
                                    try {
                                        RtxtService.this.redisCache.setCacheObject(AWS_DATA, hbaseVo);
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                }

                                //processAwsData(serialName, strs.get(i), time, resultAws);
                            }
                        } else {
                            // 处理其他非标准NMEA格式数据
                            strs.add(msg.replace("\n", " "));
                            for (int i2 = 0; i2 < strs.size(); i2++) {
                                // 创建Kafka消息对象
                                KafkaMessage receiveMessage = RtxtService.getReceiveMessage(serialName, strs.get(i2), time, 38, "038A");
                                //if (ClientListernerUtil.states == 0) {
                                //    WebSocketServer.sendRealTimeData(strs.get(i2), "COM2");
                                //}
                                // 分析数据并更新缓存(第二个参数0表示使用默认分析方式)
                                Object hbaseVo2 = BaseAnalysis.analysisData(receiveMessage, 0, resultAws);
                                if (hbaseVo2 != null) {
                                    try {
                                        RtxtService.this.redisCache.setCacheObject(AWS_DATA, hbaseVo2);
                                    } catch (Exception e2) {
                                        e2.printStackTrace();
                                    }
                                }
                            }
                        }

                        // 如果系统状态为1(显示串口数据)，将数据发送到串口视图界面
                        //if (ClientListernerUtil.states == 1) {
                        //    KafkaMessage awsKaf = RtxtService.getReceiveMessage(serialName, strs.get(0), time, 38, "038A");
                        //    RtxtService.this.clientListernerUtil.parseSerilView(awsKaf, "COM2");
                        //}
                        return;
                    }

                    // 处理/dev/ttyS0串口数据(北斗2)
                    if (serialName.equalsIgnoreCase(PORT_GPS_BD2)) {
                        String msg2 = new String(bytes, StandardCharsets.UTF_8);

                        // 处理GPS NMEA格式数据($GNRMC或$BDRMC)
                        if (msg2.startsWith("$GNRMC") || msg2.startsWith("$BDRMC") || msg2.startsWith("$GPRMC")) {
                            // 查找北斗2设备
                            Device device = RtxtService.this.findDeviceByCodePrefix("032-2A");
                            // 设置设备活跃状态缓存，有效期30秒
                            RtxtService.this.redisCache.setCacheObject("CJ_CK_COM1", "1", 30, TimeUnit.SECONDS);

                            KafkaMessage receiveMessage = getReceiveMessage(serialName, msg2, time, 32, device.getCode());

                            // 保存预览数据
                            savePreviewData(device, receiveMessage.getMsg());

                            Object hbaseVo3 = BaseAnalysis.analysisData(receiveMessage, 0, resultGps);
                            if (hbaseVo3 != null) {
                                try {
                                    RtxtService.this.redisCache.setCacheObject(GPS_DATA, hbaseVo3);
                                } catch (Exception e3) {
                                    e3.printStackTrace();
                                }
                            }

                            // 每80秒发送一次北斗2设备消息
                            if (DateUtils.fetchWholeSecond(System.currentTimeMillis()).longValue() - Long.parseLong(bd2_time) > BD2_SEND_INTERVAL) {
                                logger.info("串口发送进来1" + serialName);
                                RtxtService.this.redisCache.setCacheObject(BD2_TIME, DateUtils.fetchWholeSecond(System.currentTimeMillis()).toString());
                                if (device != null) {
                                    RtxtService.this.sendBd2Msg(device);
                                }
                                return;
                            }
                            return;
                        }
                        return;
                    }

                    // 处理/dev/ttyS3串口数据(GPS设备2-北斗3-1)
                    if (serialName.equalsIgnoreCase(PORT_BD3_1)) {
                        String msg3 = new String(bytes, StandardCharsets.UTF_8);
                        logger.info("----gps3-1-" + msg3 + "--" + msg3.startsWith("$GNRMC") + "--" + msg3.startsWith("$BDRMC"));

                        // 处理北斗3卫星导航系统NMEA格式数据($GNRMC或$BDRMC)
                        if (msg3.startsWith("$GNRMC") || msg3.startsWith("$BDRMC") || msg3.startsWith("$GPRMC")) {

                            Device device = findDeviceByCodePrefix("032-3A");

                            // 设置设备活跃状态缓存，有效期30秒
                            RtxtService.this.redisCache.setCacheObject("CJ_CK_COM4", "1", 30, TimeUnit.SECONDS);

                            // 处理GPS数据
                            KafkaMessage receiveMessage = RtxtService.getReceiveMessage(serialName, msg3, time, 32, device.getCode());

                            // 保存预览数据
                            savePreviewData(device, receiveMessage.getMsg());

                            // 分析数据并缓存结果
                            Object hbaseVo4 = BaseAnalysis.analysisData(receiveMessage, 0, resultGps);
                            if (hbaseVo4 != null) {
                                try {
                                    RtxtService.this.redisCache.setCacheObject(GPS_DATA, hbaseVo4);
                                } catch (Exception e4) {
                                    e4.printStackTrace();
                                }
                            }

                            // 每70秒发送一次北斗3-1设备消息
                            if (DateUtils.fetchWholeSecond(System.currentTimeMillis()).longValue() - Long.parseLong(bd31_time) > BD3_SEND_INTERVAL) {
                                RtxtService.this.redisCache.setCacheObject(BD3_1_TIME, DateUtils.fetchWholeSecond(System.currentTimeMillis()).toString());
                                sendBd3Msg(device);
                                return;
                            }
                            return;
                        }
                        return;
                    }

                    // 处理/dev/ttyS4串口数据(GPS设备3-北斗3-2)
                    if (serialName.equalsIgnoreCase(PORT_BD3_2)) {
                        String msg4 = new String(bytes, StandardCharsets.UTF_8);

                        logger.info("----gps3-2-" + msg4 + "--" + msg4.startsWith("$GNRMC") + "--" + msg4.startsWith("$BDRMC"));

                        // 处理北斗3卫星导航系统NMEA格式数据($GNRMC或$BDRMC)
                        if (msg4.startsWith("$GNRMC") || msg4.startsWith("$BDRMC") || msg4.startsWith("$GPRMC")) {

                            Device device = RtxtService.this.findDeviceByCodePrefix("032-3B");

                            // 设置设备活跃状态缓存，有效期30秒
                            RtxtService.this.redisCache.setCacheObject("CJ_CK_COM5", "1", 30, TimeUnit.SECONDS);

                            KafkaMessage receiveMessage = RtxtService.getReceiveMessage(serialName, msg4, time, 32, device.getCode());

                            // 保存预览数据
                            savePreviewData(device, receiveMessage.getMsg());

                            Object hbaseVo5 = BaseAnalysis.analysisData(receiveMessage, 0, resultGps);
                            if (hbaseVo5 != null) {
                                try {
                                    RtxtService.this.redisCache.setCacheObject(GPS_DATA, hbaseVo5);
                                } catch (Exception e5) {
                                    e5.printStackTrace();
                                }
                            }


                            // 每70秒发送一次北斗3-2设备消息
                            if (DateUtils.fetchWholeSecond(System.currentTimeMillis()).longValue() - Long.parseLong(bd32_time) > BD3_SEND_INTERVAL) {
                                RtxtService.this.redisCache.setCacheObject(BD3_2_TIME, DateUtils.fetchWholeSecond(System.currentTimeMillis()).toString());
                                sendBd3Msg(device);
                                return;
                            }
                            return;
                        }
                        return;
                    }

                    // 处理/dev/ttyS2串口数据(放大器设备)
                    if (serialName.equalsIgnoreCase(PORT_AMPLIFIER)) {
                        // 设置设备活跃状态缓存，有效期30秒
                        RtxtService.this.redisCache.setCacheObject("CJ_CK_COM3", "1", 30, TimeUnit.SECONDS);
                        // 将字节转换为16进制字符串
                        String msg5 = HexUtil.bytesTo16String(bytes);
                        logger.info("功放设备数据: {}", msg5);

                        // 如果接收到特定命令"110351AAFB"，发送回应命令"1603A255F7"
                        if (msg5.equalsIgnoreCase("110351AAFB")) {
                            SerialPortUtil.sendCmd("1603A255F7", serialPort);
                            return;
                        }

                        // 解析功放状态数据
                        if (msg5.length() >= 20) {
                            Amplifier amplifier = new Amplifier();
                            String data = msg5.substring(8, 20);

                            // 解析衰减值(第1-2字节)
                            String sj = data.substring(0, 2);
                            amplifier.setDecay(sj);

                            // 解析温度值(第3-4字节)
                            String wd = data.substring(2, 4);
                            amplifier.setTemp(wd);

                            // 解析输出功率(第5-8字节)
                            String sp = data.substring(4, 8);
                            amplifier.setOutPower(sp);

                            // 解析功放状态(第11-12字节)
                            String sts = data.substring(10, 12);
                            amplifier.setBucStatus(sts);

                            // 查找功放设备
                            //Device amplifierDevice = findDeviceByType(53);
                            //if (amplifierDevice != null) {
                            //    amplifier.setDeviceId(amplifierDevice.getId());
                            //}
                            //amplifier.setStatus(0L);

                            Device amplifierDevice = findDeviceByType(53);

                            // 保存到Redis
                            RtxtService.this.redisCache.setCacheObject(AMPLIFIER_DATA, amplifier);

                            // 保存预览数据
                            savePreviewData(amplifierDevice, msg5);

                            // 发送到Kafka
                            sendToKafka(amplifierDevice, amplifier, 38, "AMPLIFIER");

                            logger.info("功放设备数据解析结果: {}", JSONObject.toJSONString(amplifier));
                        }
                    } else {
                        logger.info("串口监听--名称：{}-- 数据:{}", serialName, new String(bytes));
                    }
                }
            }
        });
        return 1;
    }


    /**
     * 保存设备数据到内存中用于预览
     *
     * @param deviceEntity 设备实体
     * @param message 原始消息
     */
    private void savePreviewData(Device deviceEntity, String message) {
        // 获取设备编码
        String deviceCode = deviceEntity != null ? deviceEntity.getCode() : null;
        try {
            // 只有在设备有编码时才可能存放预览数据
            if (deviceCode != null && !deviceCode.isEmpty()) {
                // 判断该设备是否开启了预览功能
                if (DeviceController.viewKey != null && DeviceController.viewKey.containsKey(deviceCode)) {
                    // 获取当前时间并格式化
                    LocalDateTime now = LocalDateTime.now();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
                    String formattedTime = now.format(formatter);
                    // 构建key: 设备编码###时间
                    String key = deviceCode + "###" + formattedTime;

                    // 存放原始数据到viewValue
                    DeviceController.viewValue.put(key, message);
                }
            }
        } catch (Exception e) {
            logger.error("保存[{}]预览数据异常", deviceCode,e);
        }
    }


    /**
     * 发送设备数据到Kafka
     *
     * @param device 设备对象
     * @param data 设备数据对象
     * @param deviceType 设备类型ID
     * @param codePrefix 消息代码前缀
     */
    private <T> void sendToKafka(Device device, T data, Integer deviceType, String codePrefix) {
        try {
            // 创建Kafka消息对象
            KafkaMessage message = new KafkaMessage();
            message.setSn(device.getSn());
            message.setType(deviceType);
            message.setCode(codePrefix + "_DATA_" + device.getCode());
            message.setMsg(JSONObject.toJSONString(data));
            message.setInitialTime(System.currentTimeMillis());
        } catch (Exception e) {
            logger.error("设备[{}]的{}数据处理失败", device.getCode(), codePrefix, e);
        }
    }

    /**
     * 合并消息数据，将对象的属性值按顺序组合成字符串
     *
     * @param type 设备类型
     * @param obj 设备数据对象
     * @return 合并后的消息字符串，以"A"作为分隔符，如果出现异常则返回空字符串
     */
    private String mergeSendMessage(Integer type, Object obj) {
        try {
            // 参数校验
            if (type == null) {
                logger.warn("设备类型为空，无法获取默认属性");
                return "";
            }

            if (obj == null) {
                logger.warn("设备数据对象为空，无法合并消息");
                return "";
            }

            // 获取设备类型对应的属性列表
            List<String> attributes = defaultAttributeService.selectDefaultAttributeStrs(type);
            logger.info("[{}]默认属性个数---{}", type, attributes != null ? attributes.size() : 0);

            if (attributes == null || attributes.size() == 0) {
                logger.warn("设备类型[{}]没有配置默认属性", type);
                return "";
            }

            // 组合属性值
            StringBuffer sb = new StringBuffer();
            Class<?> cl = obj.getClass();

            // 遍历属性列表，通过反射获取每个属性的值
            for (String attr : attributes) {
                try {
                    // 跳过空属性名
                    if (attr == null || attr.trim().isEmpty()) {
                        logger.warn("发现空的属性名，跳过处理");
                        sb.append("A");
                        continue;
                    }

                    // 获取属性对象
                    Field field = cl.getDeclaredField(attr.trim());
                    // 设置属性可访问(即使是private属性)
                    field.setAccessible(true);

                    // 读取属性值，如果不为null则添加到结果中
                    Object fieldValue = field.get(obj);
                    if (fieldValue != null) {
                        sb.append(fieldValue.toString());
                    }

                } catch (NoSuchFieldException e) {
                    logger.warn("设备类型[{}]的对象[{}]中不存在属性[{}]，跳过该属性",
                               type, cl.getSimpleName(), attr, e);
                } catch (IllegalAccessException e) {
                    logger.warn("无法访问设备类型[{}]的对象[{}]中的属性[{}]，跳过该属性",
                               type, cl.getSimpleName(), attr, e);
                } catch (Exception e) {
                    logger.warn("处理设备类型[{}]的属性[{}]时发生未知异常，跳过该属性",
                               type, attr, e);
                }

                // 添加分隔符"A"
                sb.append("A");
            }

            // 删除最后一个分隔符(如果存在)
            if (sb.length() > 0 && sb.lastIndexOf("A") == sb.length() - 1) {
                sb.deleteCharAt(sb.lastIndexOf("A"));
            }

            String result = sb.toString();
            logger.info("设备类型[{}]消息合并结果: [{}]", type, result);
            return result;

        } catch (Exception e) {
            logger.error("合并设备类型[{}]的消息时发生异常", type, e);
            return "";
        }
    }

    /**
     * 向北斗2发送消息
     *
     * 消息格式: $CCTXA,设备ID,1,1,数据内容[C]*校验和\r\n
     * - 设备ID: 7位ASCII字符的北斗卡ID号码
     * - 数据内容: 由GPS和气象站数据组合而成，以"A"作为分隔符
     * - 数据长度限制: 69字符(超出需分段发送)
     * - 特殊字符替换: "."替换为"B"，"-"替换为"D"
     * - 终止符: "C"(仅在消息末尾时添加)
     *
     * @param device 北斗2设备
     */
    public synchronized void sendBd2Msg(Device device) {
        if (device == null) {
            logger.error("北斗2设备为空，无法发送消息");
            return;
        }

        logger.info("开始向北斗2设备[{}]发送消息", device.getCode());

        // 当前时间戳
        Long time = System.currentTimeMillis();

        // 从Redis获取设备数据
        BuMsgGps gps = redisCache.getCacheObject(GPS_DATA);
        BuMsgAws aws = redisCache.getCacheObject(AWS_DATA);

        // 组合GPS数据
        String mergeGpsSendMessage = "";
        if (gps != null) {
            mergeGpsSendMessage = mergeSendMessage(32, gps);
            logger.info("GPS数据组合结果: {}", mergeGpsSendMessage);
        }

        // 组合气象站数据
        String mergeAwsSendMessage = "";
        if (aws != null) {
            mergeAwsSendMessage = mergeSendMessage(38, aws);
            logger.info("气象站数据组合结果: {}", mergeAwsSendMessage);
        }

        // 设置默认值(如果数据为空)
        if (StringUtils.isEmpty(mergeGpsSendMessage) || "A".equalsIgnoreCase(mergeGpsSendMessage)) {
            mergeGpsSendMessage = "AA";
        }
        if (StringUtils.isEmpty(mergeAwsSendMessage) || "A".equalsIgnoreCase(mergeAwsSendMessage)) {
            mergeAwsSendMessage = "AAAAAAAA";
        }

        // 合并消息并替换特殊字符
        String mergeMessage = (mergeGpsSendMessage + "A" + mergeAwsSendMessage).replace(".", "B").replace("-", "D");

        // 从设备编号中获取北斗卡ID
        String cardId = extractCardIdFromDeviceCode(device);
        logger.info("北斗2设备[{}]使用卡ID[{}]", device.getCode(), cardId);

        // 构建消息头
        String title = "$CCTXA," + cardId + ",1,1,";

        // 处理数据发送策略
        String historyData = redisCache.getCacheObject(BD2_HISTORY);

        if (StringUtils.isNotBlank(historyData)) {
            // 有历史数据，先处理历史数据
            if (historyData.length() > BD2_MSG_LIMIT) {
                // 历史数据过长，需要分段发送
                String sendPart = historyData.substring(0, BD2_MSG_LIMIT);
                String yhStr = HexUtil.calculateNmeaXorChecksum(title + sendPart);
                String completeMessage = title + sendPart + "*" + yhStr + Sentence.TERMINATOR;

                // 打印发送给北斗的完整数据
                logger.info("北斗2设备[{}]发送完整数据: {}", device.getCode(), completeMessage);

                // 发送历史数据的前一部分
                SerialPortUtil.sendDataToComPort(
                    SerialPortUtil.getOpenPortByName(getOldSerialPort(device.getSerialPort())),
                    completeMessage.getBytes()
                );

                // 更新历史数据
                redisCache.setCacheObject(BD2_HISTORY,
                    historyData.substring(0, 7) + historyData.substring(BD2_MSG_LIMIT));

                logger.info("北斗2设备[{}]发送历史数据片段: {}", device.getCode(), sendPart);
            } else {
                // 历史数据可一次发送完
                String yhStr = HexUtil.calculateNmeaXorChecksum(title + historyData + "C");
                String completeMessage = title + historyData + "C*" + yhStr + Sentence.TERMINATOR;

                // 打印发送给北斗的完整数据
                logger.info("北斗2设备[{}]发送完整数据: {}", device.getCode(), completeMessage);

                // 发送完整历史数据
                SerialPortUtil.sendDataToComPort(
                    SerialPortUtil.getOpenPortByName(getOldSerialPort(device.getSerialPort())),
                    completeMessage.getBytes()
                );

                // 清除历史数据
                redisCache.deleteObject(BD2_HISTORY);

                logger.info("北斗2设备[{}]发送完整历史数据: {}", device.getCode(), historyData);
            }
        } else if (mergeMessage.length() > BD2_MSG_LIMIT) {
            // 没有历史数据但当前消息过长
            String sendPart = mergeMessage.substring(0, BD2_MSG_LIMIT);
            String yhStr = HexUtil.calculateNmeaXorChecksum(title + sendPart);
            String completeMessage = title + sendPart + "*" + yhStr + Sentence.TERMINATOR;

            // 打印发送给北斗的完整数据
            logger.info("北斗2设备[{}]发送完整数据: {}", device.getCode(), completeMessage);

            // 发送当前消息的前一部分
            SerialPortUtil.sendDataToComPort(
                SerialPortUtil.getOpenPortByName(getOldSerialPort(device.getSerialPort())),
                completeMessage.getBytes()
            );

            // 保存剩余部分为历史数据
            redisCache.setCacheObject(BD2_HISTORY,
                mergeMessage.substring(0, 7) + mergeMessage.substring(BD2_MSG_LIMIT));

            logger.info("北斗2设备[{}]发送消息片段: {}", device.getCode(), sendPart);
        } else {
            // 没有历史数据且当前消息可一次发送完
            String yhStr = HexUtil.calculateNmeaXorChecksum(title + mergeMessage + "C");
            String completeMessage = title + mergeMessage + "C*" + yhStr + Sentence.TERMINATOR;

            // 发送完整消息
            SerialPortUtil.sendDataToComPort(
                SerialPortUtil.getOpenPortByName(getOldSerialPort(device.getSerialPort())),
                completeMessage.getBytes()
            );
            // 打印发送给北斗的完整数据
            logger.info("北斗2设备[{}]发送完整数据: {}", device.getCode(), completeMessage);
        }

        // 记录当前发送时间
        redisCache.setCacheObject(BD2_TIME, DateUtils.fetchWholeSecond(System.currentTimeMillis()).toString());
    }

    /**
     * 向北斗3接收机发送消息（通用方法）
     *
     * 消息格式: $CCTCQ,设备ID,2,1,2,数据内容[C],0*校验和\r\n
     * - 设备ID: 7位ASCII字符的北斗卡ID号码
     * - 数据内容: 由GPS、气象站、功放等数据组合而成，以"A"作为分隔符
     * - 数据长度限制: 450字符(超出需分段发送)
     * - 特殊字符替换: "."替换为"B"，"-"替换为"D"
     * - 终止符: "C"(仅在消息末尾时添加)
     *
     * @param device 北斗3设备（通过device.getCode()区分是3A还是3B）
     */
    public synchronized void sendBd3Msg(Device device) {
        if (device == null) {
            logger.error("北斗3设备为空，无法发送消息");
            return;
        }

        // 根据设备编号确定设备类型和对应的Redis键
        String deviceCode = device.getCode();
        String deviceDisplayName;
        String historyKey;
        String timeKey;

        if (deviceCode != null && deviceCode.startsWith("032-3A")) {
            deviceDisplayName = "北斗3-1";
            historyKey = BD3_1_HISTORY;
            timeKey = BD3_1_TIME;
        } else if (deviceCode != null && deviceCode.startsWith("032-3B")) {
            deviceDisplayName = "北斗3-2";
            historyKey = BD3_2_HISTORY;
            timeKey = BD3_2_TIME;
        } else {
            logger.error("无法识别的北斗3设备编号: {}", deviceCode);
            return;
        }

        logger.info("开始向{}设备[{}]发送消息", deviceDisplayName, device.getCode());

        // 从Redis获取设备数据
        Object gps = redisCache.getCacheObject(GPS_DATA);
        Object aws = redisCache.getCacheObject(AWS_DATA);
        Object amplifier = redisCache.getCacheObject(AMPLIFIER_DATA);
        Object modem = redisCache.getCacheObject(MODEM_DATA);
        Object pdu = redisCache.getCacheObject(PDU_DATA);
        Object attitude = redisCache.getCacheObject(ATTITUDE_DATA);

        // 组合GPS数据
        String mergeGpsSendMessage = "";
        if (gps != null) {
            mergeGpsSendMessage = mergeSendMessage(32, gps);
            logger.info("GPS数据组合结果: {}", mergeGpsSendMessage);
        }

        // 组合气象站数据
        String mergeAwsSendMessage = "";
        if (aws != null) {
            mergeAwsSendMessage = mergeSendMessage(38, aws);
            logger.info("气象站数据组合结果: {}", mergeAwsSendMessage);
        }

        // 组合猫数据
        String mergeModemSendMessage = "";
        if (modem != null) {
            mergeModemSendMessage = mergeSendMessage(52, modem);
            logger.info("Modem数据组合结果: {}", mergeModemSendMessage);
        }

        // 组合PDU数据
        String mergePduSendMessage = "";
        if (pdu != null) {
            mergePduSendMessage = mergeSendMessage(51, pdu);
            logger.info("PDU数据组合结果: {}", mergePduSendMessage);
        }

        // 组合功放数据
        String mergeAmplifierSendMessage = "";
        if (amplifier != null) {
            mergeAmplifierSendMessage = mergeSendMessage(53, amplifier);
            logger.info("功放数据组合结果: {}", mergeAmplifierSendMessage);
        }

        // 组合姿态数据
        String mergeAttitudeSendMessage = "";
        if (attitude != null) {
            mergeAttitudeSendMessage = mergeSendMessage(33, attitude);
            logger.info("姿态数据组合结果: {}", mergeAttitudeSendMessage);
        }

        // 设置默认值(如果数据为空)
        if (StringUtils.isEmpty(mergeGpsSendMessage) || "A".equalsIgnoreCase(mergeGpsSendMessage)) {
            mergeGpsSendMessage = "AA";
        }
        if (StringUtils.isEmpty(mergeAwsSendMessage) || "A".equalsIgnoreCase(mergeAwsSendMessage)) {
            mergeAwsSendMessage = "AAAAAAAA";
        }
        if (StringUtils.isEmpty(mergeModemSendMessage) || "A".equalsIgnoreCase(mergeModemSendMessage)) {
            mergeModemSendMessage = "AAA";
        }
        if (StringUtils.isEmpty(mergeAmplifierSendMessage) || "A".equalsIgnoreCase(mergeAmplifierSendMessage)) {
            mergeAmplifierSendMessage = "AAA";
        }
        if (StringUtils.isEmpty(mergePduSendMessage) || "A".equalsIgnoreCase(mergePduSendMessage)) {
            mergePduSendMessage = "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAA";
        }
        if (StringUtils.isEmpty(mergeAttitudeSendMessage) || "A".equalsIgnoreCase(mergeAttitudeSendMessage)) {
            mergeAttitudeSendMessage = "AAAAAA";
        }


        // 合并所有设备数据，以"A"作为分隔符，并替换特殊字符
        // "."替换为"B"，"-"替换为"D"
        String mergeMessage = (mergeGpsSendMessage + "A" + mergeAwsSendMessage + "A" + mergeModemSendMessage + "A" + mergeAmplifierSendMessage + "A" + mergePduSendMessage + "A" + mergeAttitudeSendMessage).replace(".", "B").replace("-", "D");

        // 从设备编号中获取北斗卡ID
        String cardId = extractCardIdFromDeviceCode(device);
        logger.info("{}设备[{}]使用卡ID[{}]", deviceDisplayName, device.getCode(), cardId);

        // 构建消息头
        String title = "CCTCQ," + cardId + ",2,1,2,";

        // 处理数据发送策略
        String historyData = redisCache.getCacheObject(historyKey);

        if (StringUtils.isNotBlank(historyData)) {
            // 有历史数据，先处理历史数据
            if (historyData.length() > BD3_MSG_LIMIT) {
                // 历史数据过长，需要分段发送
                String sendPart = historyData.substring(0, BD3_MSG_LIMIT);
                String yhStr = HexUtil.calculateXorChecksum(title + sendPart + ",0");
                String completeMessage = "$" + title + sendPart + ",0*" + yhStr + Sentence.TERMINATOR;

                // 打印发送给北斗的完整数据
                logger.info("{}设备[{}]发送完整数据: {}", deviceDisplayName, device.getCode(), completeMessage);

                // 发送历史数据的前一部分
                SerialPortUtil.sendDataToComPort(
                    SerialPortUtil.getOpenPortByName(getOldSerialPort(device.getSerialPort())),
                    completeMessage.getBytes()
                );

                // 更新历史数据
                redisCache.setCacheObject(historyKey,
                    historyData.substring(0, 7) + historyData.substring(BD3_MSG_LIMIT));

                logger.info("{}设备[{}]发送历史数据片段: {}", deviceDisplayName, device.getCode(), sendPart);
            } else {
                // 历史数据可一次发送完
                String yhStr = HexUtil.calculateXorChecksum(title + historyData + "C,0");
                String completeMessage = "$" + title + historyData + "C,0*" + yhStr + Sentence.TERMINATOR;

                // 打印发送给北斗的完整数据
                logger.info("{}设备[{}]发送完整数据: {}", deviceDisplayName, device.getCode(), completeMessage);

                // 发送完整历史数据
                SerialPortUtil.sendDataToComPort(
                    SerialPortUtil.getOpenPortByName(getOldSerialPort(device.getSerialPort())),
                    completeMessage.getBytes()
                );

                // 清除历史数据
                redisCache.deleteObject(historyKey);

                logger.info("{}设备[{}]发送完整历史数据: {}", deviceDisplayName, device.getCode(), historyData);
            }
        } else if (mergeMessage.length() > BD3_MSG_LIMIT) {
            // 没有历史数据但当前消息过长
            String sendPart = mergeMessage.substring(0, BD3_MSG_LIMIT);
            String yhStr = HexUtil.calculateXorChecksum(title + sendPart + ",0");
            String completeMessage = "$" + title + sendPart + ",0*" + yhStr + Sentence.TERMINATOR;

            // 打印发送给北斗的完整数据
            logger.info("{}设备[{}]发送完整数据: {}", deviceDisplayName, device.getCode(), completeMessage);

            // 发送当前消息的前一部分
            SerialPortUtil.sendDataToComPort(
                SerialPortUtil.getOpenPortByName(getOldSerialPort(device.getSerialPort())),
                completeMessage.getBytes()
            );

            // 保存剩余部分为历史数据
            redisCache.setCacheObject(historyKey,
                mergeMessage.substring(0, 7) + mergeMessage.substring(BD3_MSG_LIMIT));

            logger.info("{}设备[{}]发送消息片段: {}", deviceDisplayName, device.getCode(), sendPart);
        } else {
            // 没有历史数据且当前消息可一次发送完
            String yhStr = HexUtil.calculateXorChecksum(title + mergeMessage + "C,0");
            String completeMessage = "$" + title + mergeMessage + "C,0*" + yhStr + Sentence.TERMINATOR;

            logger.info("###############3:"+device.getSerialPort()+"####"+getOldSerialPort(device.getSerialPort()));
            // 发送完整消息
            SerialPortUtil.sendDataToComPort(
                    SerialPortUtil.getOpenPortByName(getOldSerialPort(device.getSerialPort())),
                    completeMessage.getBytes()
            );

            // 打印发送给北斗的完整数据
            logger.info("{}设备[{}]发送完整数据: {}", deviceDisplayName, device.getCode(), completeMessage);

        }

        // 记录当前发送时间
        redisCache.setCacheObject(timeKey, DateUtils.fetchWholeSecond(System.currentTimeMillis()).toString());
    }

    /**
     * 向北斗设备发送内容混编消息（编码类别3）
     *
     * 消息格式: $CCTCQ,设备ID,3,1,3,十六进制内容,0*校验和\r\n
     * - 设备ID: 7位ASCII字符的北斗卡ID号码
     * - 编码类别: 3（内容混编）
     * - 十六进制内容: 使用GB2312编码的HEX格式，BCD码以'A4'开头
     * - 数据长度限制: 450字符(超出需分段发送)
     *
     * @param device 北斗设备
     * @param content 要发送的文本内容
     */
    public synchronized void sendMixedEncodingMsg(Device device, String content) {
        if (device == null) {
            logger.error("北斗设备为空，无法发送内容混编消息");
            return;
        }

        if (content == null || content.trim().isEmpty()) {
            logger.warn("发送内容为空，使用默认内容");
            content = "测试内容";
        }

        logger.info("开始向北斗设备[{}]发送内容混编消息，内容: {}", device.getCode(), content);

        // 将内容转换为GB2312编码的十六进制字符串
        String hexContent = HexUtil.stringToGB2312Hex(content);
        logger.info("内容转换为GB2312十六进制: {}", hexContent);

        // 从设备编号中获取北斗卡ID
        String cardId = extractCardIdFromDeviceCode(device);
        logger.info("北斗设备[{}]使用卡ID[{}]", device.getCode(), cardId);

        // 构建消息头（编码类别3）
        String title = "CCTCQ," + cardId + ",3,1,3,";

        // 检查内容长度是否超出限制
        if (hexContent.length() > BD3_MSG_LIMIT) {
            // 内容过长，需要分段发送
            String sendPart = hexContent.substring(0, BD3_MSG_LIMIT);
            String yhStr = HexUtil.calculateXorChecksum(title + sendPart + ",0");
            String completeMessage = "$" + title + sendPart + ",0*" + yhStr + Sentence.TERMINATOR;

            // 打印发送给北斗的完整数据
            logger.info("北斗设备[{}]发送完整数据: {}", device.getCode(), completeMessage);

            // 发送消息的前一部分
            SerialPortUtil.sendDataToComPort(
                SerialPortUtil.getOpenPortByName(getOldSerialPort(device.getSerialPort())),
                completeMessage.getBytes()
            );

            logger.info("北斗设备[{}]发送内容混编消息片段: {}", device.getCode(), sendPart);
            logger.warn("内容过长，剩余部分未发送: {}", hexContent.substring(BD3_MSG_LIMIT));
        } else {
            // 内容可一次发送完
            String yhStr = HexUtil.calculateXorChecksum(title + hexContent + ",0");
            String completeMessage = "$" + title + hexContent + ",0*" + yhStr + Sentence.TERMINATOR;

            // 发送完整消息
            SerialPortUtil.sendDataToComPort(
                SerialPortUtil.getOpenPortByName(getOldSerialPort(device.getSerialPort())),
                completeMessage.getBytes()
            );

            // 打印发送给北斗的完整数据
            logger.info("北斗设备[{}]发送完整数据: {}", device.getCode(), completeMessage);
        }

        logger.info("北斗设备[{}]内容混编消息发送完成", device.getCode());
    }


    /**
     * 获取接收到的Kafka消息对象
     *
     * @return KafkaMessage对象
     */
    public static KafkaMessage getReceiveMessage(String serialName, String receiveMessage, Long time, Integer type, String code) {
        if (receiveMessage.contains("@@")) {
            receiveMessage = receiveMessage.substring(8);
        }
        if (receiveMessage.contains("\n\t")) {
            receiveMessage = receiveMessage.replace("\n", "").replace("\t", "");
        }
        KafkaMessage kafkaMessage = new KafkaMessage(type, code, receiveMessage, 10, time);
        return kafkaMessage;
    }

    /**
     * 根据设备类型查找设备
     *
     * @param type 设备类型
     * @return 设备对象
     */
    private Device findDeviceByType(long type) {
        Device query = new Device();
        query.setType(type);
        query.setEnable(1); // 启用状态
        List<Device> devices = deviceService.selectDeviceList(query);
        return devices != null && !devices.isEmpty() ? devices.get(0) : null;
    }

    /**
     * 根据设备编号前缀查找设备
     * 用于查找北斗设备，因为设备编号格式为：032-2A-15950041
     * 需要根据前缀032-2A来查找对应的设备
     *
     * @param codePrefix 设备编号前缀（如：032-2A、032-3A、032-3B）
     * @return 设备对象，如果找不到返回null
     */
    private Device findDeviceByCodePrefix(String codePrefix) {
        if (StringUtils.isBlank(codePrefix)) {
            logger.warn("设备编号前缀为空，无法查找设备");
            return null;
        }

        // 查询所有启用的设备
        Device query = new Device();
        query.setEnable(1); // 启用状态
        List<Device> devices = deviceService.selectDeviceList(query);

        if (devices == null || devices.isEmpty()) {
            return null;
        }

        // 遍历设备列表，查找编号以指定前缀开头的设备
        for (Device device : devices) {
            if (device.getCode() != null && device.getCode().startsWith(codePrefix)) {
                return device;
            }
        }

        logger.warn("没有找到编号前缀为[{}]的设备", codePrefix);
        return null;
    }

    /**
     * 从设备编号中提取北斗卡号
     * 设备编号格式：032-2A-15950041 或 032-3A-15950041
     * 提取最后一段作为北斗卡号
     *
     * @param device 设备对象
     * @return 北斗卡号，如果提取失败返回默认值"15950041"
     */
    private String extractCardIdFromDeviceCode(Device device) {
        if (device == null || StringUtils.isBlank(device.getCode())) {
            logger.warn("设备或设备编号为空，使用默认北斗卡号");
            //4217502   海油大厦
            //15950041  海聊平台
            return "4217502";
        }
        String deviceCode = device.getCode().trim();
        // 按"-"分割设备编号
        String[] parts = deviceCode.split("-");
        if (parts.length >= 3) {
            String cardId = parts[parts.length - 1]; // 取最后一段
            if (StringUtils.isNotBlank(cardId) && cardId.matches("\\d+")) {
                return cardId;
            }
        }
        logger.warn("无法从设备编号[{}]中提取有效的北斗卡号，使用默认值", deviceCode);
        return "15950041";
    }

    //public static void save2Txt(KafkaMessage message) {
    //    String directory = sourceDataPathNew + "/" + DeviceTypeEnum.getByValue(message.getType().intValue()).getAlias() + "-" + message.getCode();
    //    File file = new File(directory);
    //    if (!file.exists()) {
    //        file.mkdirs();
    //    }
    //    logger.info("保存开始1---{}", directory);
    //    String fileFullPath = directory + "/" + com.xhjt.common.utils.DateUtils.getDate() + ".txt";
    //    logger.info("保存开始2---{}", fileFullPath);
    //    try {
    //        PrintWriter fw = new PrintWriter(new BufferedWriter(new FileWriter(fileFullPath, true)));
    //        logger.info("保存开始2---{}", fileFullPath);
    //        String timeStr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date(message.getInitialTime().longValue()));
    //        fw.println("@&" + timeStr + "&@");
    //        fw.println(message.getMsg());
    //        fw.flush();
    //        fw.close();
    //    } catch (Exception e) {
    //        System.out.println();
    //        logger.error("写入文件时报错！", e);
    //    }
    //}

}
