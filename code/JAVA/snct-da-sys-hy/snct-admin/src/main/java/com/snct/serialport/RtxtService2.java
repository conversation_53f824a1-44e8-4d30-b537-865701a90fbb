package com.snct.serialport;

import com.alibaba.fastjson2.JSONObject;
import com.snct.analysis.BaseAnalysis;
import com.snct.analysis.vo.AttitudeHbaseVo2;
import com.snct.analysis.vo.AwsHbaseVo1;
import com.snct.analysis.vo.GpsHbaseVo;
import com.snct.common.constant.CacheConstants;
import com.snct.common.core.redis.RedisCache;
import com.snct.common.utils.DateUtils;
import com.snct.common.utils.spring.SpringUtils;
import com.snct.device.domain.Amplifier;
import com.snct.device.manager.DeviceConnectionManager;
import com.snct.kafka.KafkaMessage;
import com.snct.system.domain.Device;
import com.snct.system.domain.SerialConfig;
import com.snct.system.domain.data.BuDataAmplifier;
import com.snct.system.domain.data.BuDataAttitude;
import com.snct.system.domain.data.BuDataAws;
import com.snct.system.domain.data.BuDataGps;
import com.snct.system.domain.msg.BuMsgGps;
import com.snct.system.domain.msg.aws.BuMsgAws;
import com.snct.system.service.*;
import com.snct.system.service.impl.SerialConfigServiceImpl;
import com.snct.utils.HexUtil;
import com.snct.web.controller.business.DeviceController;
import com.snct.utils.DeviceRawDataFileUtil;
import gnu.io.SerialPort;
import gnu.io.SerialPortEvent;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 串口通信服务类 - 优化版本
 * 本服务负责管理多个串口设备的通信，支持从设备配置动态读取串口参数
 *
 * <AUTHOR>
 */
@Service
public class RtxtService2 {
    private static final Logger logger = LoggerFactory.getLogger(RtxtService2.class);

    @Autowired
    private IDeviceService deviceService;

    @Autowired
    private IDefaultAttributeService defaultAttributeService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private DeviceConnectionManager connectionManager;

    @Autowired
    private IBuDataGpsService buDataGpsService;

    @Autowired
    private IBuDataAwsService buDataAwsService;

    @Autowired
    private IBuDataAttitudeService buDataAttitudeService;

    @Autowired
    private IBuDataAmplifierService buDataAmplifierService;


    // 设备类型与串口处理器的映射缓存
    private final Map<String, Device> serialPortDeviceMap = new ConcurrentHashMap<>();

    // 设备类型常量
    private static final long DEVICE_TYPE_GPS_BD2 = 32L;      // 北斗2/GPS设备
    private static final long DEVICE_TYPE_AWS = 38L;          // 气象站设备
    private static final long DEVICE_TYPE_AMPLIFIER = 53L;    // 功放设备
    private static final long DEVICE_TYPE_ATTITUDE = 33L;     // 姿态设备

    // 北斗发送相关Redis键名
    private static final String BD2_TIME = CacheConstants.BD_SEND_TIME + "bd2";
    private static final String BD3_1_TIME = CacheConstants.BD_SEND_TIME + "bd3_1";
    private static final String BD3_2_TIME = CacheConstants.BD_SEND_TIME + "bd3_2";
    private static final String BD2_HISTORY = CacheConstants.DEVICE_HISTORY_DATA_KEY + "bd2";
    private static final String BD3_1_HISTORY = CacheConstants.DEVICE_HISTORY_DATA_KEY + "bd3_1";
    private static final String BD3_2_HISTORY = CacheConstants.DEVICE_HISTORY_DATA_KEY + "bd3_2";

    // 设备数据缓存键名
    private static final String GPS_DATA = CacheConstants.DEVICE_DATA_KEY + "gps";
    private static final String AWS_DATA = CacheConstants.DEVICE_DATA_KEY + "aws";
    private static final String AMPLIFIER_DATA = CacheConstants.DEVICE_DATA_KEY + "amplifier";
    private static final String MODEM_DATA = CacheConstants.DEVICE_DATA_KEY + "modem";
    private static final String PDU_DATA = CacheConstants.DEVICE_DATA_KEY + "pdu";
    private static final String ATTITUDE_DATA = CacheConstants.DEVICE_DATA_KEY + "attitude";

    // 消息长度限制默认值
    private static final int DEFAULT_BD2_MSG_LIMIT = 69;      // 北斗2消息长度限制默认值(69)
    private static final int DEFAULT_BD3_MSG_LIMIT = 220;     // 北斗3消息长度限制默认值(220)

    // 默认发送间隔(毫秒)
    private static final long DEFAULT_BD2_SEND_INTERVAL = 125000; // 125秒
    private static final long DEFAULT_BD3_SEND_INTERVAL = 125000; // 125秒

    // 判断当前系统类型
    private static final boolean IS_WINDOWS = System.getProperty("os.name").toLowerCase().contains("win");

    /**
     * 初始化并处理所有串口服务
     * 1. 清除Redis中的时间缓存
     * 2. 从设备配置中动态加载串口配置
     * 3. 清除已打开的串口映射
     * 4. 为每个配置的设备串口设置监听器
     */
    public void handleAllService() {
        logger.info("开始初始化串口服务");

        // 清除Redis中的时间缓存
        this.redisCache.deleteObject(BD2_TIME);
        this.redisCache.deleteObject(BD3_1_TIME);
        this.redisCache.deleteObject(BD3_2_TIME);

        // 查找系统所有串口
        SerialPortUtil.findSystemAllSerialPort();

        // 清除已打开的串口映射和设备映射缓存
        SerialPortUtil.cleanOpenMap();
        serialPortDeviceMap.clear();

        // 从设备配置中加载串口配置
        initializeSerialPorts();
    }

    /**
     * 从设备配置中初始化串口
     * 根据设备配置动态建立串口与设备的映射关系
     */
    private void initializeSerialPorts() {
        try {
            // 查询所有启用的设备
            Device query = new Device();
            query.setEnable(1);
            List<Device> devices = deviceService.selectDeviceList(query);

            if (devices == null || devices.isEmpty()) {
                logger.warn("没有找到启用的设备，串口初始化跳过");
                return;
            }

            logger.info("找到{}个启用的设备，开始初始化串口配置", devices.size());

            for (Device device : devices) {
                if (StringUtils.isNotBlank(device.getSerialPort())) {
                    try {
                        // 创建串口配置并初始化监听器
                        SerialPortConfig config = createSerialPortConfig(device);
                        if (config != null) {
                            toListenerSerialPort(config);
                            logger.info("设备[{}]串口[{}]初始化成功", device.getName(), config.getPortName());
                        }
                    } catch (Exception e) {
                        logger.error("设备[{}]串口[{}]初始化失败", device.getName(), device.getSerialPort(), e);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("串口服务初始化失败", e);
        }
    }


    /**
     * 根据设备配置创建串口配置对象
     */
    private SerialPortConfig createSerialPortConfig(Device device) {
        if (device == null || StringUtils.isBlank(device.getSerialPort())) {
            return null;
        }

        // 获取实际串口名称
        String actualPortName = device.getSerialPort();
        if (!IS_WINDOWS) {
            actualPortName = getOldSerialPort(device.getSerialPort());
        }

        if (StringUtils.isBlank(actualPortName)) {
            logger.warn("设备[{}]的串口[{}]无法映射到实际串口名称", device.getName(), device.getSerialPort());
            return null;
        }

        // 从设备配置中获取串口参数，提供默认值
        int baudRate = getDeviceBaudRate(device);
        int dataBits = device.getDataBits() != null ? device.getDataBits() : 8;
        int stopBits = device.getStopBits() != null ? device.getStopBits() : 1;
        int parity = device.getParity() != null ? device.getParity() : 0;

        logger.info("设备[{}]串口配置: 端口={}, 波特率={}, 数据位={}, 停止位={}, 校验位={}",
                device.getName(), actualPortName, baudRate, dataBits, stopBits, parity);

        return new SerialPortConfig(actualPortName, baudRate, dataBits, stopBits, parity, device);
    }

    /**
     * 根据设备类型获取默认波特率
     * 保持与原有逻辑的兼容性
     */
    private int getDeviceBaudRate(Device device) {
        // 优先使用设备配置的波特率
        if (device.getBaudRate() != null && device.getBaudRate() > 0) {
            return device.getBaudRate();
        }

        // 根据设备类型提供默认波特率
        if (device.getType() != null) {
            switch (device.getType().intValue()) {
                case 32: // GPS/北斗设备
                    return 19200;
                case 38: // 气象站设备
                    return 4800;
                case 53: // 功放设备
                    return 9600;
                default:
                    return 4800; // 默认波特率
            }
        }

        return 4800; // 最终默认值
    }

    /**
     * 获取设备的消息长度限制
     * 优先使用设备配置的消息长度，如果没有配置则使用默认值
     *
     * @param device 设备对象
     * @return 消息长度限制
     */
    private int getDeviceMessageLimit(Device device) {
        // 优先使用设备配置的消息长度
        if (device.getMsgLength() != null && device.getMsgLength() > 0) {
            return device.getMsgLength();
        }

        // 根据设备编号确定默认消息长度限制
        String deviceCode = device.getCode();
        if (deviceCode != null) {
            if (deviceCode.startsWith("032-2A")) {
                // 北斗2设备
                return DEFAULT_BD2_MSG_LIMIT;
            } else if (deviceCode.startsWith("032-3A") || deviceCode.startsWith("032-3B")) {
                // 北斗3设备
                return DEFAULT_BD3_MSG_LIMIT;
            }
        }

        // 如果无法识别设备类型，根据设备类型返回默认值
        if (device.getType() != null && device.getType() == DEVICE_TYPE_GPS_BD2) {
            return DEFAULT_BD2_MSG_LIMIT;
        }

        // 最终默认值
        return DEFAULT_BD3_MSG_LIMIT;
    }

    /**
     * 为指定串口配置设置监听器
     *
     * @param config 串口配置对象
     * @return 操作结果：1-成功，0-失败
     */
    public synchronized Integer toListenerSerialPort(SerialPortConfig config) {
        if (config == null || StringUtils.isEmpty(config.getPortName())) {
            logger.warn("串口配置为空或串口名称为空");
            return 0;
        }

        String serialName = config.getPortName();

        // 打开串口
        final SerialPort serialPort = SerialPortUtil.openComPort(
                serialName,
                config.getBaudRate(),
                config.getDataBits(),
                config.getStopBits(),
                config.getParity()
        );

        if (serialPort == null) {
            logger.error("串口[{}]打开失败", serialName);
            return 0;
        }

        SerialPortUtil.addOpenPort(serialName, serialPort);

        // 建立串口与设备的映射关系
        if (config.getDevice() != null) {
            serialPortDeviceMap.put(serialName, config.getDevice());
        }

        // 为串口设置事件监听器
        SerialPortUtil.setListenerToSerialPort(serialPort, arg0 -> {
            if (arg0.getEventType() == SerialPortEvent.DATA_AVAILABLE) {

                // 更新统一连接状态管理
                Device device = config.getDevice();
                if (device != null) {
                    connectionManager.updateDeviceConnectionStatus(device,
                        DeviceConnectionManager.ConnectionStatus.CONNECTED,
                        DeviceConnectionManager.ProtocolType.SERIAL);
                }

                handleSerialPortData(serialName, serialPort, config.getDevice());
            }
        });
        return 1;
    }

    // =======================数据处理=======================

    /**
     * 处理串口数据
     *
     * @param serialName 串口名称
     * @param serialPort 串口对象
     * @param device     关联的设备对象
     */
    private void handleSerialPortData(String serialName, SerialPort serialPort, Device device) {
        try {
            // 获取各设备的时间缓存
            initializeTimeCache();

            // 读取串口数据
            byte[] bytes = SerialPortUtil.readData(serialPort, 20);
            Long initTime = System.currentTimeMillis();

            // 根据设备类型分发处理
            handleDataByDeviceType(device, serialName, bytes, initTime);

        } catch (Exception e) {
            logger.error("处理串口[{}]数据时发生异常", serialName, e);
        }
    }

    /**
     * 根据设备类型处理数据
     */
    private void handleDataByDeviceType(Device device, String serialName, byte[] bytes, Long initialTime) {
        if (device.getType() == null) {
            logger.warn("设备[{}]类型为空，无法处理数据", device.getName());
            return;
        }

        // 保存原始数据
        String msg = new String(bytes, StandardCharsets.UTF_8);
        DeviceRawDataFileUtil.saveRawDataToFile(device, msg, initialTime);

        long deviceType = device.getType();

        switch ((int) deviceType) {
            case 32: // GPS/北斗设备
                handleGpsData(device, bytes, initialTime);
                break;
            case 33: // 姿态仪设备
                handleAttitudeData(device, bytes, initialTime);
                break;
            case 38: // 气象站设备
                handleAwsData(device, bytes, initialTime);
                break;
            case 53: // 功放设备
                handleAmplifierData(device, serialName, bytes, initialTime);
                break;
            default:
                //String msg = new String(bytes, StandardCharsets.UTF_8);
                savePreviewData(device, msg);
                logger.info("未知设备类型[{}]，串口[{}]，数据: {}", deviceType, serialName, new String(bytes));
                break;
        }
    }

    /**
     * 初始化时间缓存
     */
    private void initializeTimeCache() {
        String bd2_time = this.redisCache.getCacheObject(BD2_TIME);
        String bd31_time = this.redisCache.getCacheObject(BD3_1_TIME);
        String bd32_time = this.redisCache.getCacheObject(BD3_2_TIME);

        if (StringUtils.isBlank(bd2_time)) {
            this.redisCache.setCacheObject(BD2_TIME,
                    DateUtils.fetchWholeSecond(System.currentTimeMillis()).toString());
        }
        if (StringUtils.isBlank(bd31_time)) {
            this.redisCache.setCacheObject(BD3_1_TIME,
                    DateUtils.fetchWholeSecond(System.currentTimeMillis()).toString());
        }
        if (StringUtils.isBlank(bd32_time)) {
            this.redisCache.setCacheObject(BD3_2_TIME,
                    DateUtils.fetchWholeSecond(System.currentTimeMillis()).toString());
        }
    }

    /**
     * 处理气象站数据
     */
    private void handleAwsData(Device device, byte[] bytes, Long time) {

        List<String> strs = new ArrayList<>();
        String msg = new String(bytes, StandardCharsets.UTF_8);

        // 保存预览数据
        savePreviewData(device, msg);
        logger.info("----{}----{}", device.getName(), msg);

        Object resultAws = this.redisCache.getCacheObject(AWS_DATA);

        // 处理气象站NMEA格式数据($WIMWV和$WIXDR)
        if (msg.contains("$WIMWV") || msg.contains("$WIXDR")) {
            if (msg.contains("$WIMWV") && msg.contains("$WIXDR")) {
                if (msg.contains("\n\t")) {
                    msg = msg.replace("\n", "").replace("\t", "");
                }
                strs.add(msg.substring(msg.indexOf("$WIMWV"), msg.indexOf("$WIXDR")));
                strs.add(msg.substring(msg.indexOf("$WIXDR"), msg.length()));
            } else {
                strs.add(msg);
            }

            for (int i = 0; i < strs.size(); i++) {
                KafkaMessage receiveMessage = getReceiveMessage(strs.get(i), time, 38, device != null ?
                        device.getCode() : "038A");
                Object analysisData = BaseAnalysis.analysisData(receiveMessage, 1, resultAws);
                //logger.info("{}解析结果---{}", device.getName(), JSONObject.toJSONString(analysisData));
                if (analysisData != null) {
                    try {
                        this.redisCache.setCacheObject(AWS_DATA, analysisData);

                        // 保存数据到数据库
                        BuDataAws buDataAws = new BuDataAws();
                        AwsHbaseVo1 analysisData1 = (AwsHbaseVo1) analysisData;
                        buDataAws.setDeviceId(Math.toIntExact(device.getId()));
                        buDataAws.setDeviceCode(device.getCode());
                        buDataAws.setDeviceName(device.getName());

                        if (StringUtils.isNotBlank(analysisData1.getInitialTime())) {
                            buDataAws.setInitialTime(Long.valueOf(analysisData1.getInitialTime()));
                            buDataAws.setInitialBjTime(new Date(Long.valueOf(analysisData1.getInitialTime())));
                        }
                        if (StringUtils.isNotBlank(analysisData1.getRelativeWind()) && isNumeric(analysisData1.getRelativeWind())) {
                            buDataAws.setRelativeWind(BigDecimal.valueOf(Double.parseDouble(analysisData1.getRelativeWind())));
                        }
                        if (StringUtils.isNotBlank(analysisData1.getRelativeWindSpeed()) && isNumeric(analysisData1.getRelativeWindSpeed())) {
                            buDataAws.setRelativeWindSpeed(BigDecimal.valueOf(Double.parseDouble(analysisData1.getRelativeWindSpeed())));
                        }
                        if (StringUtils.isNotBlank(analysisData1.getWindSpeedUnit())) {
                            buDataAws.setWindSpeedUnit(analysisData1.getWindSpeedUnit());
                        }
                        if (StringUtils.isNotBlank(analysisData1.getAirTemperature()) && isNumeric(analysisData1.getAirTemperature())) {
                            buDataAws.setAirTemperature(BigDecimal.valueOf(Double.parseDouble(analysisData1.getAirTemperature())));
                        }

                        if (StringUtils.isNotBlank(analysisData1.getHumidity()) && isNumeric(analysisData1.getHumidity())) {
                            buDataAws.setHumidity(BigDecimal.valueOf(Double.parseDouble(analysisData1.getHumidity())));
                        }
                        //buDataAws.setDewPoint(BigDecimal.valueOf(Double.parseDouble(analysisData1.getDp())));
                        //buDataAws.setPressure(BigDecimal.valueOf(Double.parseDouble(analysisData1.getPressure())));
                        if (StringUtils.isNotBlank(analysisData1.getQfe()) && isNumeric(analysisData1.getQfe())) {
                            buDataAws.setQfe(BigDecimal.valueOf(Double.parseDouble(analysisData1.getQfe())));
                        }
                        if (StringUtils.isNotBlank(analysisData1.getQnh()) && isNumeric(analysisData1.getQnh())) {
                            buDataAws.setQnh(BigDecimal.valueOf(Double.parseDouble(analysisData1.getQnh())));
                        }

                        buDataAwsService.insertBuDataAws(buDataAws);


                    } catch (Exception e) {
                        logger.error("缓存气象站数据失败", e);
                    }
                }
            }
        } else {
            strs.add(msg.replace("\n", " "));
            for (int i2 = 0; i2 < strs.size(); i2++) {
                KafkaMessage receiveMessage = getReceiveMessage(strs.get(i2), time, 38, device != null ?
                        device.getCode() : "038A");
                Object analysisData = BaseAnalysis.analysisData(receiveMessage, 0, resultAws);
                //logger.info("{}解析结果---{}", device.getName(), JSONObject.toJSONString(analysisData));
                if (analysisData != null) {
                    try {
                        this.redisCache.setCacheObject(AWS_DATA, analysisData);
                    } catch (Exception e2) {
                        logger.error("缓存气象站数据失败", e2);
                    }
                }
            }
        }
    }

    /**
     * 处理GPS/北斗数据
     */
    private void handleGpsData(Device device, byte[] bytes, Long initialTime) {
        String msg = new String(bytes, StandardCharsets.UTF_8);

        // 处理GPS NMEA格式数据($GNRMC或$BDRMC)
        if (msg.startsWith("$GNRMC") || msg.startsWith("$BDRMC") || msg.startsWith("$GPRMC")) {

            // 保存预览数据
            savePreviewData(device, msg);
            logger.info("----{}----{}", device.getName(), msg);

            KafkaMessage receiveMessage = getReceiveMessage(msg, initialTime, 32, device.getCode());

            Object resultGps = this.redisCache.getCacheObject(GPS_DATA);

            Object analysisData = BaseAnalysis.analysisData(receiveMessage, 0, resultGps);

            //logger.info("{}解析结果---{}", device.getName(), JSONObject.toJSONString(analysisData));

            if (analysisData != null) {
                try {
                    this.redisCache.setCacheObject(GPS_DATA, analysisData);

                    // 保存数据到数据库
                    BuDataGps buDataGps = new BuDataGps();
                    GpsHbaseVo gpsHbaseVo = (GpsHbaseVo) analysisData;
                    buDataGps.setDeviceId(Math.toIntExact(device.getId()));
                    buDataGps.setDeviceCode(device.getCode());
                    buDataGps.setDeviceName(device.getName());
                    buDataGps.setInitialTime(initialTime);
                    buDataGps.setInitialBjTime(new Date(initialTime));

                    if (StringUtils.isNotBlank(gpsHbaseVo.getUtcTime())) {
                        buDataGps.setUtcTime(gpsHbaseVo.getUtcTime());
                    }
                    if (StringUtils.isNotBlank(gpsHbaseVo.getLatitudeHemisphere())) {
                        buDataGps.setLatitudeHemisphere(gpsHbaseVo.getLatitudeHemisphere());
                    }
                    if (StringUtils.isNotBlank(gpsHbaseVo.getLongitudeHemisphere())) {
                        buDataGps.setLongitudeHemisphere(gpsHbaseVo.getLongitudeHemisphere());
                    }
                    if (StringUtils.isNotBlank(gpsHbaseVo.getLatitude()) && isNumeric(gpsHbaseVo.getLatitude())) {
                        buDataGps.setLatitude(BigDecimal.valueOf(Double.parseDouble(gpsHbaseVo.getLatitude())));
                    }
                    if (StringUtils.isNotBlank(gpsHbaseVo.getLongitude()) && isNumeric(gpsHbaseVo.getLongitude())) {
                        buDataGps.setLongitude(BigDecimal.valueOf(Double.parseDouble(gpsHbaseVo.getLongitude())));
                    }

                    buDataGpsService.insertBuDataGps(buDataGps);


                } catch (Exception e) {
                    logger.error("缓存GPS数据失败", e);
                }
            }

            // 根据设备类型发送北斗消息
            handleBeidouMessageSending(device);
        } else {
            logger.info("---{}---[其他数据格式]: {}",device.getName(), msg);
        }
    }

    /**
     * 处理功放设备数据
     */
    private void handleAmplifierData(Device device, String serialName, byte[] bytes, Long initialTime) {
        // 将字节转换为16进制字符串
        String msg = HexUtil.bytesTo16String(bytes);
        // 保存预览数据
        savePreviewData(device, msg);
        logger.debug("----{}----{}", device.getName(), msg);

        // 如果接收到特定命令"110351AAFB"，发送回应命令"1603A255F7"
        if (msg.equalsIgnoreCase("110351AAFB")) {
            SerialPort serialPort = SerialPortUtil.getOpenPortByName(serialName);
            if (serialPort != null) {
                SerialPortUtil.sendCmd("1603A255F7", serialPort);
            }
            return;
        }

        // 解析功放状态数据
        if (msg.length() >= 20) {
            Amplifier amplifier = new Amplifier();
            String data = msg.substring(8, 20);

            // 解析各项数据
            amplifier.setDecay(String.valueOf(HexUtil.hexToInt(data.substring(0, 2))));
            amplifier.setTemp(String.valueOf(HexUtil.hexToInt(data.substring(2, 4))));
            amplifier.setOutPower(String.valueOf(HexUtil.hexToInt(data.substring(4, 8)) / 10.0));
            amplifier.setBucStatus(String.valueOf(HexUtil.hexToInt(data.substring(10, 12))));

            // 保存到Redis
            this.redisCache.setCacheObject(AMPLIFIER_DATA, amplifier);

            // 保存数据到数据库
            BuDataAmplifier buDataAmplifier = new BuDataAmplifier();
            buDataAmplifier.setDeviceId(Math.toIntExact(device.getId()));
            buDataAmplifier.setDeviceCode(device.getCode());
            buDataAmplifier.setDeviceName(device.getName());
            buDataAmplifier.setInitialTime(initialTime);
            buDataAmplifier.setInitialBjTime(new Date(initialTime));

            if (StringUtils.isNotBlank(amplifier.getBucStatus()) && isNumeric(amplifier.getBucStatus())) {
                buDataAmplifier.setDeviceStatus(Integer.parseInt(amplifier.getBucStatus()));
            }
            if (StringUtils.isNotBlank(amplifier.getDecay()) && isNumeric(amplifier.getDecay())) {
                buDataAmplifier.setDecay(BigDecimal.valueOf(Double.parseDouble(amplifier.getDecay())));
            }
            if (StringUtils.isNotBlank(amplifier.getTemp()) && isNumeric(amplifier.getTemp())) {
                buDataAmplifier.setTemperature(BigDecimal.valueOf(Double.parseDouble(amplifier.getTemp())));
            }
            if (StringUtils.isNotBlank(amplifier.getOutPower()) && isNumeric(amplifier.getOutPower())) {
                buDataAmplifier.setOutputPower(BigDecimal.valueOf(Double.parseDouble(amplifier.getOutPower())));
            }

            buDataAmplifierService.insertBuDataAmplifier(buDataAmplifier);

            //logger.info("{}解析结果---{}", device.getName(), JSONObject.toJSONString(amplifier));
        }
    }

    /**
     * 处理姿态数据
     */
    private void handleAttitudeData(Device device, byte[] bytes, Long initialTime) {
        String msg = new String(bytes, StandardCharsets.UTF_8);
        savePreviewData(device, msg);
        logger.info("----{}----{}", device.getName(), msg);
        KafkaMessage receiveMessage = getReceiveMessage(msg, initialTime, 33, device.getCode());
        Object resultAttitude = BaseAnalysis.analysisData(receiveMessage, 0, null);
        logger.info("{}解析结果---{}", device.getName(), JSONObject.toJSONString(resultAttitude));

        // 保存数据到数据库
        BuDataAttitude buDataAttitude = new BuDataAttitude();
        AttitudeHbaseVo2 attitudeHbaseVo = (AttitudeHbaseVo2) resultAttitude;
        buDataAttitude.setDeviceId(Math.toIntExact(device.getId()));
        buDataAttitude.setDeviceCode(device.getCode());
        buDataAttitude.setDeviceName(device.getName());
        buDataAttitude.setInitialTime(initialTime);
        buDataAttitude.setInitialBjTime(new Date(initialTime));

        if (StringUtils.isNotBlank(attitudeHbaseVo.getSerialNo())) {
            buDataAttitude.setSerialNo(attitudeHbaseVo.getSerialNo());
        }
        if (StringUtils.isNotBlank(attitudeHbaseVo.getUtc())) {
            buDataAttitude.setUtc(attitudeHbaseVo.getUtc());
        }
        if (StringUtils.isNotBlank(attitudeHbaseVo.getLat())) {
            buDataAttitude.setLat(attitudeHbaseVo.getLat());
        }
        if (StringUtils.isNotBlank(attitudeHbaseVo.getLon())) {
            buDataAttitude.setLon(attitudeHbaseVo.getLon());
        }
        if (StringUtils.isNotBlank(attitudeHbaseVo.getElpHeight())) {
            buDataAttitude.setElpHeight(attitudeHbaseVo.getElpHeight());
        }
        if (StringUtils.isNotBlank(attitudeHbaseVo.getHeading())) {
            buDataAttitude.setHeading(attitudeHbaseVo.getHeading());
        }
        if (StringUtils.isNotBlank(attitudeHbaseVo.getPitch())) {
            buDataAttitude.setPitch(attitudeHbaseVo.getPitch());
        }
        if (StringUtils.isNotBlank(attitudeHbaseVo.getRolling())) {
            buDataAttitude.setRolling(attitudeHbaseVo.getRolling());
        }
        if (StringUtils.isNotBlank(attitudeHbaseVo.getVelN())) {
            buDataAttitude.setVelN(attitudeHbaseVo.getVelN());
        }
        if (StringUtils.isNotBlank(attitudeHbaseVo.getVelE())) {
            buDataAttitude.setVelE(attitudeHbaseVo.getVelE());
        }
        if (StringUtils.isNotBlank(attitudeHbaseVo.getVelD())) {
            buDataAttitude.setVelD(attitudeHbaseVo.getVelD());
        }
        if (StringUtils.isNotBlank(attitudeHbaseVo.getVelG())) {
            buDataAttitude.setVelG(attitudeHbaseVo.getVelG());
        }
        if (StringUtils.isNotBlank(attitudeHbaseVo.getCoordinateNorthing())) {
            buDataAttitude.setCoordinateNorthing(attitudeHbaseVo.getCoordinateNorthing());
        }
        if (StringUtils.isNotBlank(attitudeHbaseVo.getCoordinateEasting())) {
            buDataAttitude.setCoordinateEasting(attitudeHbaseVo.getCoordinateEasting());
        }
        if (StringUtils.isNotBlank(attitudeHbaseVo.getNorthDistance())) {
            buDataAttitude.setNorthDistance(attitudeHbaseVo.getNorthDistance());
        }
        if (StringUtils.isNotBlank(attitudeHbaseVo.getEastDistance())) {
            buDataAttitude.setEastDistance(attitudeHbaseVo.getEastDistance());
        }
        if (StringUtils.isNotBlank(attitudeHbaseVo.getPositionIndicator())) {
            buDataAttitude.setPositionIndicator(attitudeHbaseVo.getPositionIndicator());
        }
        if (StringUtils.isNotBlank(attitudeHbaseVo.getHeadingIndicator())) {
            buDataAttitude.setHeadingIndicator(attitudeHbaseVo.getHeadingIndicator());
        }
        if (StringUtils.isNotBlank(attitudeHbaseVo.getSvn())) {
            buDataAttitude.setSvn(attitudeHbaseVo.getSvn());
        }
        if (StringUtils.isNotBlank(attitudeHbaseVo.getSolutionSv())) {
            buDataAttitude.setSolutionSv(attitudeHbaseVo.getSolutionSv());
        }
        if (StringUtils.isNotBlank(attitudeHbaseVo.getDiffAge())) {
            buDataAttitude.setDiffAge(attitudeHbaseVo.getDiffAge());
        }
        if (StringUtils.isNotBlank(attitudeHbaseVo.getStationId())) {
            buDataAttitude.setStationId(attitudeHbaseVo.getStationId());
        }
        if (StringUtils.isNotBlank(attitudeHbaseVo.getBaselineLength())) {
            buDataAttitude.setBaselineLength(attitudeHbaseVo.getBaselineLength());
        }

        buDataAttitudeService.insertBuDataAttitude(buDataAttitude);


    }

    // =======================消息发送=======================

    /**
     * 处理北斗消息发送逻辑
     */
    private void handleBeidouMessageSending(Device device) {
        if (device == null || device.getCode() == null) {
            return;
        }

        String deviceCode = device.getCode();

        // 获取时间缓存
        String bd2_time = this.redisCache.getCacheObject(BD2_TIME);
        String bd31_time = this.redisCache.getCacheObject(BD3_1_TIME);
        String bd32_time = this.redisCache.getCacheObject(BD3_2_TIME);

        // 北斗2设备
        if (deviceCode.startsWith("032-2A")) {
            long sendInterval = getDeviceSendInterval(device, "BD2");
            if (bd2_time != null && DateUtils.fetchWholeSecond(System.currentTimeMillis()).longValue() - Long.parseLong(bd2_time) > sendInterval) {
                logger.info("串口发送进来1 " + device.getSerialPort());
                this.redisCache.setCacheObject(BD2_TIME,
                        DateUtils.fetchWholeSecond(System.currentTimeMillis()).toString());
                sendBd2Msg(device);
            }
        }
        // 北斗3-1设备
        else if (deviceCode.startsWith("032-3A")) {
            long sendInterval = getDeviceSendInterval(device, "BD3");
            if (bd31_time != null && DateUtils.fetchWholeSecond(System.currentTimeMillis()).longValue() - Long.parseLong(bd31_time) > sendInterval) {
                this.redisCache.setCacheObject(BD3_1_TIME,
                        DateUtils.fetchWholeSecond(System.currentTimeMillis()).toString());
                sendBd3Msg(device);
            }
        }
        // 北斗3-2设备
        else if (deviceCode.startsWith("032-3B")) {
            long sendInterval = getDeviceSendInterval(device, "BD3");
            if (bd32_time != null && DateUtils.fetchWholeSecond(System.currentTimeMillis()).longValue() - Long.parseLong(bd32_time) > sendInterval) {
                this.redisCache.setCacheObject(BD3_2_TIME,
                        DateUtils.fetchWholeSecond(System.currentTimeMillis()).toString());
                sendBd3Msg(device);
            }
        }
    }

    /**
     * 获取设备的发送间隔（毫秒）
     * 优先使用设备配置的compartment字段，如果为空或0则使用默认值
     *
     * @param device 设备对象
     * @param bdType 北斗类型（BD2、BD3）
     * @return 发送间隔（毫秒）
     */
    private long getDeviceSendInterval(Device device, String bdType) {
        if (device == null || device.getCompartment() == null || device.getCompartment() <= 0) {
            // 使用默认间隔
            if ("BD2".equals(bdType)) {
                return DEFAULT_BD2_SEND_INTERVAL;
            } else {
                return DEFAULT_BD3_SEND_INTERVAL;
            }
        }

        // 将设备配置的秒数转换为毫秒
        long intervalMs = device.getCompartment() * 1000;
        logger.debug("设备[{}]使用配置的发送间隔: {}秒({}毫秒)", device.getCode(), device.getCompartment(), intervalMs);
        return intervalMs;
    }

    /**
     * 合并消息数据，将对象的属性值按顺序组合成字符串
     */
    private String mergeSendMessage(Integer type, Object obj) {
        try {
            if (type == null) {
                logger.warn("设备类型为空，无法获取默认属性");
                return "";
            }

            if (obj == null) {
                logger.warn("设备数据对象为空，无法合并消息");
                return "";
            }

            List<String> attributes = defaultAttributeService.selectDefaultAttributeStrs(type);
            logger.info("[{}]默认属性---{}:{}", type, attributes != null ? attributes.size() : 0, attributes);

            if (attributes == null || attributes.size() == 0) {
                logger.warn("设备类型[{}]没有配置默认属性", type);
                return "";
            }

            StringBuffer sb = new StringBuffer();
            Class<?> cl = obj.getClass();

            for (String attr : attributes) {
                try {
                    if (attr == null || attr.trim().isEmpty()) {
                        logger.warn("发现空的属性名，跳过处理");
                        sb.append("A");
                        continue;
                    }

                    Field field = cl.getDeclaredField(attr.trim());
                    field.setAccessible(true);

                    Object fieldValue = field.get(obj);
                    if (fieldValue != null) {
                        sb.append(fieldValue.toString());
                    }

                } catch (NoSuchFieldException e) {
                    logger.warn("设备类型[{}]的对象[{}]中不存在属性[{}]，跳过该属性", type, cl.getSimpleName(), attr, e);
                } catch (IllegalAccessException e) {
                    logger.warn("无法访问设备类型[{}]的对象[{}]中的属性[{}]，跳过该属性", type, cl.getSimpleName(), attr, e);
                } catch (Exception e) {
                    logger.warn("处理设备类型[{}]的属性[{}]时发生未知异常，跳过该属性", type, attr, e);
                }

                sb.append("A");
            }

            if (sb.length() > 0 && sb.lastIndexOf("A") == sb.length() - 1) {
                sb.deleteCharAt(sb.lastIndexOf("A"));
            }

            String result = sb.toString();
            logger.info("设备类型[{}]消息合并结果: [{}]", type, result);
            return result;

        } catch (Exception e) {
            logger.error("合并设备类型[{}]的消息时发生异常", type, e);
            return "";
        }
    }

    /**
     * 北斗2发送消息
     */
    public synchronized void sendBd2Msg(Device device) {
        if (device == null) {
            logger.error("北斗2设备为空，无法发送消息");
            return;
        }

        logger.info("开始向北斗2设备[{}]发送消息", device.getCode());

        // 从Redis获取设备数据
        BuMsgGps gps = redisCache.getCacheObject(GPS_DATA);
        BuMsgAws aws = redisCache.getCacheObject(AWS_DATA);

        // 组合GPS数据
        String mergeGpsSendMessage = "";
        if (gps != null) {
            mergeGpsSendMessage = mergeSendMessage(32, gps);
            logger.info("GPS数据组合结果: {}", mergeGpsSendMessage);
        }

        // 组合气象站数据
        String mergeAwsSendMessage = "";
        if (aws != null) {
            mergeAwsSendMessage = mergeSendMessage(38, aws);
            logger.info("气象站数据组合结果: {}", mergeAwsSendMessage);
        }

        // 设置默认值
        if (StringUtils.isEmpty(mergeGpsSendMessage) || "A".equalsIgnoreCase(mergeGpsSendMessage)) {
            mergeGpsSendMessage = "AA";
        }
        if (StringUtils.isEmpty(mergeAwsSendMessage) || "A".equalsIgnoreCase(mergeAwsSendMessage)) {
            mergeAwsSendMessage = "AAAAAAAA";
        }

        // 合并消息并替换特殊字符
        String mergeMessage = (mergeGpsSendMessage + "A" + mergeAwsSendMessage).replace(".", "B").replace("-", "D");

        // 从设备编号中获取北斗卡ID
        String cardId = extractCardIdFromDeviceCode(device);
        logger.info("北斗2设备[{}]使用卡ID[{}]", device.getCode(), cardId);

        // 构建消息头
        String title = "$CCTXA," + cardId + ",1,1,";

        // 获取设备的消息长度限制
        int msgLimit = getDeviceMessageLimit(device);
        logger.debug("北斗2设备[{}]使用消息长度限制: {}", device.getCode(), msgLimit);

        // 处理数据发送策略
        String historyData = redisCache.getCacheObject(BD2_HISTORY);

        if (StringUtils.isNotBlank(historyData)) {
            if (historyData.length() > msgLimit) {
                String sendPart = historyData.substring(0, msgLimit);
                String yhStr = HexUtil.calculateNmeaXorChecksum(title + sendPart);
                String completeMessage = title + sendPart + "*" + yhStr;

                logger.debug("北斗2设备[{}]开始发送数据: {}", device.getCode(), completeMessage);

                //SerialPortUtil.sendDataToComPort(
                //        SerialPortUtil.getOpenPortByName(getOldSerialPort(device.getSerialPort())),
                //        completeMessage.getBytes()
                //);
                SerialPortUtil.sendDataByCommand(completeMessage, getOldSerialPort(device.getSerialPort()));

                redisCache.setCacheObject(BD2_HISTORY,
                        historyData.substring(0, 7) + historyData.substring(msgLimit));

                logger.info("北斗2设备[{}]发送历史数据片段成功: {}", device.getCode(), completeMessage);
            } else {
                String yhStr = HexUtil.calculateNmeaXorChecksum(title + historyData + "C");
                String completeMessage = title + historyData + "C*" + yhStr;

                logger.debug("北斗2设备[{}]开始发送数据: {}", device.getCode(), completeMessage);

                //SerialPortUtil.sendDataToComPort(
                //        SerialPortUtil.getOpenPortByName(getOldSerialPort(device.getSerialPort())),
                //        completeMessage.getBytes()
                //);
                SerialPortUtil.sendDataByCommand(completeMessage, getOldSerialPort(device.getSerialPort()));

                redisCache.deleteObject(BD2_HISTORY);

                logger.info("北斗2设备[{}]发送完整历史数据成功: {}", device.getCode(), completeMessage);
            }
        } else if (mergeMessage.length() > msgLimit) {
            String sendPart = mergeMessage.substring(0, msgLimit);
            String yhStr = HexUtil.calculateNmeaXorChecksum(title + sendPart);
            String completeMessage = title + sendPart + "*" + yhStr;

            logger.debug("北斗2设备[{}]开始发送数据: {}", device.getCode(), completeMessage);

            //SerialPortUtil.sendDataToComPort(
            //        SerialPortUtil.getOpenPortByName(getOldSerialPort(device.getSerialPort())),
            //        completeMessage.getBytes()
            //);
            SerialPortUtil.sendDataByCommand(completeMessage, getOldSerialPort(device.getSerialPort()));

            redisCache.setCacheObject(BD2_HISTORY,
                    mergeMessage.substring(0, 7) + mergeMessage.substring(msgLimit));

            logger.info("北斗2设备[{}]发送消息片段数据成功: {}", device.getCode(), completeMessage);
        } else {
            String yhStr = HexUtil.calculateNmeaXorChecksum(title + mergeMessage + "C");
            String completeMessage = title + mergeMessage + "C*" + yhStr;

            logger.debug("北斗2设备[{}]开始发送完整数据: {}", device.getCode(), completeMessage);

            //SerialPortUtil.sendDataToComPort(
            //        SerialPortUtil.getOpenPortByName(getOldSerialPort(device.getSerialPort())),
            //        completeMessage.getBytes()
            //);
            SerialPortUtil.sendDataByCommand(completeMessage, getOldSerialPort(device.getSerialPort()));

            logger.info("北斗2设备[{}]发送完整数据: {}", device.getCode(), completeMessage);
        }

        redisCache.setCacheObject(BD2_TIME,
                DateUtils.fetchWholeSecond(System.currentTimeMillis()).toString());
    }

    /**
     * 北斗3发送消息
     */
    public synchronized void sendBd3Msg(Device device) {
        if (device == null) {
            logger.error("北斗3设备为空，无法发送消息");
            return;
        }

        // 根据设备编号确定设备类型和对应的Redis键
        String deviceCode = device.getCode();
        String deviceDisplayName;
        String historyKey;
        String timeKey;

        if (deviceCode != null && deviceCode.startsWith("032-3A")) {
            deviceDisplayName = "北斗3-1";
            historyKey = BD3_1_HISTORY;
            timeKey = BD3_1_TIME;
        } else if (deviceCode != null && deviceCode.startsWith("032-3B")) {
            deviceDisplayName = "北斗3-2";
            historyKey = BD3_2_HISTORY;
            timeKey = BD3_2_TIME;
        } else {
            logger.error("无法识别的北斗3设备编号: {}", deviceCode);
            return;
        }

        logger.info("开始向{}设备[{}]发送消息", deviceDisplayName, device.getCode());

        // 从Redis获取设备数据
        Object gps = redisCache.getCacheObject(GPS_DATA);
        Object aws = redisCache.getCacheObject(AWS_DATA);
        Object amplifier = redisCache.getCacheObject(AMPLIFIER_DATA);
        Object modem = redisCache.getCacheObject(MODEM_DATA);
        Object pdu = redisCache.getCacheObject(PDU_DATA);
        Object attitude = redisCache.getCacheObject(ATTITUDE_DATA);

        // 组合各类设备数据
        String mergeGpsSendMessage = gps != null ? mergeSendMessage(32, gps) : "";
        String mergeAwsSendMessage = aws != null ? mergeSendMessage(38, aws) : "";
        String mergeModemSendMessage = modem != null ? mergeSendMessage(52, modem) : "";
        String mergeAmplifierSendMessage = amplifier != null ? mergeSendMessage(53, amplifier) : "";
        String mergePduSendMessage = pdu != null ? mergeSendMessage(51, pdu) : "";
        String mergeAttitudeSendMessage = attitude != null ? mergeSendMessage(33, attitude) : "";

        // 设置默认值
        if (StringUtils.isEmpty(mergeGpsSendMessage) || "A".equalsIgnoreCase(mergeGpsSendMessage)) {
            mergeGpsSendMessage = "AA";
        }
        if (StringUtils.isEmpty(mergeAwsSendMessage) || "A".equalsIgnoreCase(mergeAwsSendMessage)) {
            mergeAwsSendMessage = "AAAAAAAA";
        }
        if (StringUtils.isEmpty(mergeModemSendMessage) || "A".equalsIgnoreCase(mergeModemSendMessage)) {
            mergeModemSendMessage = "AAA";
        }
        if (StringUtils.isEmpty(mergeAmplifierSendMessage) || "A".equalsIgnoreCase(mergeAmplifierSendMessage)) {
            mergeAmplifierSendMessage = "AAA";
        }
        if (StringUtils.isEmpty(mergePduSendMessage) || "A".equalsIgnoreCase(mergePduSendMessage)) {
            mergePduSendMessage = "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAA";
        }
        if (StringUtils.isEmpty(mergeAttitudeSendMessage) || "A".equalsIgnoreCase(mergeAttitudeSendMessage)) {
            mergeAttitudeSendMessage = "AAAAAA";
        }

        // 合并所有设备数据
        String mergeMessage =
                (mergeGpsSendMessage + "A" + mergeAwsSendMessage + "A" + mergeModemSendMessage + "A" + mergeAmplifierSendMessage + "A" + mergePduSendMessage + "A" + mergeAttitudeSendMessage).replace(".", "B").replace("-", "D");

        // 从设备编号中获取北斗卡ID
        String cardId = extractCardIdFromDeviceCode(device);
        logger.info("{}设备[{}]使用卡ID[{}]", deviceDisplayName, device.getCode(), cardId);

        // 构建消息头
        String title = "CCTCQ," + cardId + ",2,1,2,";

        // 获取设备的消息长度限制
        int msgLimit = getDeviceMessageLimit(device);
        logger.debug("{}设备[{}]使用消息长度限制: {}", deviceDisplayName, device.getCode(), msgLimit);

        // 处理数据发送策略
        String historyData = redisCache.getCacheObject(historyKey);

        if (StringUtils.isNotBlank(historyData)) {
            if (historyData.length() > msgLimit) {
                String sendPart = historyData.substring(0, msgLimit);
                String yhStr = HexUtil.calculateXorChecksum(title + sendPart + ",0");
                String completeMessage = "$" + title + sendPart + ",0*" + yhStr;

                logger.debug("{}设备[{}]开始发送数据: {}", deviceDisplayName, device.getCode(), completeMessage);
                //SerialPortUtil.sendDataToComPort(
                //        SerialPortUtil.getOpenPortByName(getOldSerialPort(device.getSerialPort())),
                //        completeMessage.getBytes()
                //);
                SerialPortUtil.sendDataByCommand(completeMessage, getOldSerialPort(device.getSerialPort()));
                redisCache.setCacheObject(historyKey,
                        historyData.substring(0, 7) + historyData.substring(msgLimit));
                logger.info("{}设备[{}]发送历史数据片段成功: {}", deviceDisplayName, device.getCode(), completeMessage);

            } else {
                String yhStr = HexUtil.calculateXorChecksum(title + historyData + "C,0");
                String completeMessage = "$" + title + historyData + "C,0*" + yhStr;

                logger.debug("{}设备[{}]开始发送数据: {}", deviceDisplayName, device.getCode(), completeMessage);
                //SerialPortUtil.sendDataToComPort(
                //        SerialPortUtil.getOpenPortByName(getOldSerialPort(device.getSerialPort())),
                //        completeMessage.getBytes()
                //);
                SerialPortUtil.sendDataByCommand(completeMessage, getOldSerialPort(device.getSerialPort()));
                redisCache.deleteObject(historyKey);
                logger.info("{}设备[{}]发送完整历史数据成功: {}", deviceDisplayName, device.getCode(), completeMessage);

            }
        } else if (mergeMessage.length() > msgLimit) {
            String sendPart = mergeMessage.substring(0, msgLimit);
            String yhStr = HexUtil.calculateXorChecksum(title + sendPart + ",0");
            String completeMessage = "$" + title + sendPart + ",0*" + yhStr;

            logger.debug("{}设备[{}]开始发送数据: {}", deviceDisplayName, device.getCode(), completeMessage);
            //SerialPortUtil.sendDataToComPort(
            //        SerialPortUtil.getOpenPortByName(getOldSerialPort(device.getSerialPort())),
            //        completeMessage.getBytes()
            //);
            SerialPortUtil.sendDataByCommand(completeMessage, getOldSerialPort(device.getSerialPort()));
            redisCache.setCacheObject(historyKey,
                    mergeMessage.substring(0, 7) + mergeMessage.substring(msgLimit));
            logger.info("{}设备[{}]发送消息片段成功: {}", deviceDisplayName, device.getCode(), completeMessage);

        } else {
            String yhStr = HexUtil.calculateXorChecksum(title + mergeMessage + "C,0");
            String completeMessage = "$" + title + mergeMessage + "C,0*" + yhStr;

            logger.info("{}设备[{}]开始发送数据: {}", deviceDisplayName, device.getCode(), completeMessage);
            //SerialPortUtil.sendDataToComPort(
            //        SerialPortUtil.getOpenPortByName(getOldSerialPort(device.getSerialPort())),
            //        completeMessage.getBytes()
            //);
            SerialPortUtil.sendDataByCommand(completeMessage, getOldSerialPort(device.getSerialPort()));
            logger.info("{}设备[{}]发送完整数据成功: {}", deviceDisplayName, device.getCode(), completeMessage);
        }

        redisCache.setCacheObject(timeKey, DateUtils.fetchWholeSecond(System.currentTimeMillis()).toString());
    }

    /**
     * 向北斗设备发送内容混编消息（编码类别3）
     */
    public synchronized void sendMixedEncodingMsg(Device device, String content) {
        if (device == null) {
            logger.error("北斗设备为空，无法发送内容混编消息");
            return;
        }

        if (content == null || content.trim().isEmpty()) {
            logger.warn("发送内容为空，使用默认内容");
            content = "测试内容";
        }

        logger.info("开始向北斗设备[{}]发送内容混编消息，内容: {}", device.getCode(), content);

        String hexContent = HexUtil.stringToGB2312Hex(content);
        logger.info("内容转换为GB2312十六进制: {}", hexContent);

        String cardId = extractCardIdFromDeviceCode(device);
        logger.info("北斗设备[{}]使用卡ID[{}]", device.getCode(), cardId);

        String title = "CCTCQ," + cardId + ",3,1,3,";

        // 获取设备的消息长度限制
        int msgLimit = getDeviceMessageLimit(device);
        logger.debug("北斗设备[{}]使用消息长度限制: {}", device.getCode(), msgLimit);

        if (hexContent.length() > msgLimit) {
            String sendPart = hexContent.substring(0, msgLimit);
            String yhStr = HexUtil.calculateXorChecksum(title + sendPart + ",0");
            String completeMessage = "$" + title + sendPart + ",0*" + yhStr;

            logger.info("北斗设备[{}]发送完整数据: {}", device.getCode(), completeMessage);

            //SerialPortUtil.sendDataToComPort(
            //        SerialPortUtil.getOpenPortByName(getOldSerialPort(device.getSerialPort())),
            //        completeMessage.getBytes()
            //);
            SerialPortUtil.sendDataByCommand(completeMessage, getOldSerialPort(device.getSerialPort()));

            logger.info("北斗设备[{}]发送内容混编消息片段: {}", device.getCode(), sendPart);
            logger.warn("内容过长，剩余部分未发送: {}", hexContent.substring(msgLimit));
        } else {
            String yhStr = HexUtil.calculateXorChecksum(title + hexContent + ",0");
            String completeMessage = "$" + title + hexContent + ",0*" + yhStr;

            //SerialPortUtil.sendDataToComPort(
            //        SerialPortUtil.getOpenPortByName(getOldSerialPort(device.getSerialPort())),
            //        completeMessage.getBytes()
            //);
            SerialPortUtil.sendDataByCommand(completeMessage, getOldSerialPort(device.getSerialPort()));

            logger.info("北斗设备[{}]发送完整数据: {}", device.getCode(), completeMessage);
        }

        logger.info("北斗设备[{}]内容混编消息发送完成", device.getCode());
    }

    /**
     * 发送设备数据到Kafka
     */
    private <T> void sendToKafka(Device device, T data, Integer deviceType, String codePrefix) {
        try {
            KafkaMessage message = new KafkaMessage();
            message.setSn(device.getSn());
            message.setType(deviceType);
            message.setCode(codePrefix + "_DATA_" + device.getCode());
            message.setMsg(JSONObject.toJSONString(data));
            message.setInitialTime(System.currentTimeMillis());
        } catch (Exception e) {
            logger.error("设备[{}]的{}数据处理失败", device.getCode(), codePrefix, e);
        }
    }

    /**
     * 获取接收到的Kafka消息对象
     */
    public static KafkaMessage getReceiveMessage(String receiveMessage, Long time, Integer type,
                                                 String code) {
        if (receiveMessage.contains("@@")) {
            receiveMessage = receiveMessage.substring(8);
        }
        if (receiveMessage.contains("\n\t")) {
            receiveMessage = receiveMessage.replace("\n", "").replace("\t", "");
        }
        KafkaMessage kafkaMessage = new KafkaMessage(type, code, receiveMessage, 10, time);
        return kafkaMessage;
    }


    // ================================================================================================

    /**
     * 保存设备数据到内存中用于预览
     *
     * @param deviceEntity 设备实体
     * @param message      原始消息
     */
    private void savePreviewData(Device deviceEntity, String message) {
        String deviceCode = deviceEntity != null ? deviceEntity.getCode() : null;
        try {
            if (deviceCode != null && !deviceCode.isEmpty()) {
                if (DeviceController.viewKey != null && DeviceController.viewKey.containsKey(deviceCode)) {
                    LocalDateTime now = LocalDateTime.now();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
                    String formattedTime = now.format(formatter);
                    String key = deviceCode + "###" + formattedTime;
                    DeviceController.viewValue.put(key, message);
                }
            }
        } catch (Exception e) {
            logger.error("保存[{}]预览数据异常", deviceCode, e);
        }
    }



    /**
     * 根据新的串口名称获取对应的旧串口名称
     *
     * @param newSerialPort 新的串口名称
     * @return 对应的旧串口名称，如果没有找到则返回null
     */
    public static String getOldSerialPort(String newSerialPort) {
        ISerialConfigService serialConfigService = SpringUtils.getBean(SerialConfigServiceImpl.class);
        if (StringUtils.isNotBlank(newSerialPort)) {
            SerialConfig se = new SerialConfig();
            se.setNewName(newSerialPort);
            SerialConfig serialConfig = serialConfigService.selectSerialConfig(se);
            if (serialConfig != null) {
                String oldSerialName = serialConfig.getOldName();
                return oldSerialName;
            }
            return null;
        }
        return null;
    }

    /**
     * 根据设备类型查找设备
     */
    private Device findDeviceByType(long type) {
        Device query = new Device();
        query.setType(type);
        query.setEnable(1);
        List<Device> devices = deviceService.selectDeviceList(query);
        return devices != null && !devices.isEmpty() ? devices.get(0) : null;
    }

    /**
     * 根据设备编号前缀查找设备
     */
    private Device findDeviceByCodePrefix(String codePrefix) {
        if (StringUtils.isBlank(codePrefix)) {
            logger.warn("设备编号前缀为空，无法查找设备");
            return null;
        }

        Device query = new Device();
        query.setEnable(1);
        List<Device> devices = deviceService.selectDeviceList(query);

        if (devices == null || devices.isEmpty()) {
            return null;
        }

        for (Device device : devices) {
            if (device.getCode() != null && device.getCode().startsWith(codePrefix)) {
                return device;
            }
        }

        logger.warn("没有找到编号前缀为[{}]的设备", codePrefix);
        return null;
    }

    /**
     * 从设备编号中提取北斗卡号
     */
    private String extractCardIdFromDeviceCode(Device device) {
        if (device == null || StringUtils.isBlank(device.getCode())) {
            logger.warn("设备或设备编号为空，使用默认北斗卡号");
            return "4217502";
        }
        String deviceCode = device.getCode().trim();
        String[] parts = deviceCode.split("-");
        if (parts.length >= 3) {
            String cardId = parts[parts.length - 1];
            if (StringUtils.isNotBlank(cardId) && cardId.matches("\\d+")) {
                return cardId;
            }
        }
        logger.warn("无法从设备编号[{}]中提取有效的北斗卡号，使用默认值", deviceCode);
        return "15950041";
    }


    // ==================== 公共接口方法 ====================

    /**
     * 获取串口设备映射信息（用于监控和调试）
     */
    public Map<String, Device> getSerialPortDeviceMap() {
        return new HashMap<>(serialPortDeviceMap);
    }

    /**
     * 重新加载设备配置（支持热更新）
     * 完整重启串口服务以应用新的设备配置
     */
    public void reloadDeviceConfig() {
        logger.info("开始重新加载设备配置");

        try {
            // 1. 关闭所有已打开的串口
            logger.info("关闭所有已打开的串口连接");
            SerialPortUtil.cleanOpenMap();

            // 2. 清除设备映射缓存
            serialPortDeviceMap.clear();

            // 3. 重新查找系统串口
            logger.info("重新扫描系统串口");
            SerialPortUtil.findSystemAllSerialPort();

            // 4. 重新初始化串口配置和监听器
            logger.info("重新初始化串口配置");
            initializeSerialPorts();

            logger.info("设备配置重新加载完成，当前活跃串口数量: {}", serialPortDeviceMap.size());

        } catch (Exception e) {
            logger.error("重新加载设备配置时发生异常", e);
            throw new RuntimeException("重新加载设备配置失败", e);
        }
    }

    /**
     * 获取当前活跃的串口设备数量
     */
    public int getActiveSerialPortCount() {
        return serialPortDeviceMap.size();
    }

    /**
     * 检查字符串是否为有效的数字格式
     *
     * @param str 待检查的字符串
     * @return 如果是有效数字返回true，否则返回false
     */
    private boolean isNumeric(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
