package com.snct.snmp;

import org.snmp4j.smi.OctetString;

/**
 * SNMP配置类
 * 存储SNMP连接所需的配置参数
 * 
 * <AUTHOR>
 */
public class SnmpConfig {

    private String community = "public";
    
    // SNMP版本
    private int version = 1;
    
    // 重试次数
    private int retries = 2;
    
    // 超时时间(毫秒)
    private long timeout = 1500;
    
    // 设备IP地址
    private String ipAddress;
    
    // 设备端口
    private int port = 161;
    
    // 设备唯一标识（可以是设备ID或编码）
    private String deviceId;
    
    /**
     * 创建默认配置
     */
    public SnmpConfig() {
    }
    
    /**
     * 创建配置（指定IP和端口）
     * 
     * @param ipAddress IP地址
     * @param port 端口
     */
    public SnmpConfig(String ipAddress, int port) {
        this.ipAddress = ipAddress;
        this.port = port;
    }
    
    /**
     * 创建配置（指定IP、端口和设备ID）
     * 
     * @param ipAddress IP地址
     * @param port 端口
     * @param deviceId 设备ID
     */
    public SnmpConfig(String ipAddress, int port, String deviceId) {
        this.ipAddress = ipAddress;
        this.port = port;
        this.deviceId = deviceId;
    }
    
    /**
     * 获取SNMP社区字符串
     * 
     * @return SNMP社区
     */
    public OctetString getCommunity() {
        return new OctetString(community);
    }
    
    /**
     * 设置SNMP社区
     * 
     * @param community SNMP社区
     * @return 当前配置对象
     */
    public SnmpConfig setCommunity(String community) {
        this.community = community;
        return this;
    }

    /**
     * 获取SNMP版本
     * 
     * @return SNMP版本
     */
    public int getVersion() {
        return version;
    }

    /**
     * 设置SNMP版本
     * 
     * @param version SNMP版本
     * @return 当前配置对象
     */
    public SnmpConfig setVersion(int version) {
        this.version = version;
        return this;
    }

    /**
     * 获取重试次数
     * 
     * @return 重试次数
     */
    public int getRetries() {
        return retries;
    }

    /**
     * 设置重试次数
     * 
     * @param retries 重试次数
     * @return 当前配置对象
     */
    public SnmpConfig setRetries(int retries) {
        this.retries = retries;
        return this;
    }

    /**
     * 获取超时时间
     * 
     * @return 超时时间(毫秒)
     */
    public long getTimeout() {
        return timeout;
    }

    /**
     * 设置超时时间
     * 
     * @param timeout 超时时间(毫秒)
     * @return 当前配置对象
     */
    public SnmpConfig setTimeout(long timeout) {
        this.timeout = timeout;
        return this;
    }

    /**
     * 获取设备IP地址
     * 
     * @return IP地址
     */
    public String getIpAddress() {
        return ipAddress;
    }

    /**
     * 设置设备IP地址
     * 
     * @param ipAddress IP地址
     * @return 当前配置对象
     */
    public SnmpConfig setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
        return this;
    }

    /**
     * 获取设备端口
     * 
     * @return 端口
     */
    public int getPort() {
        return port;
    }

    /**
     * 设置设备端口
     * 
     * @param port 端口
     * @return 当前配置对象
     */
    public SnmpConfig setPort(int port) {
        this.port = port;
        return this;
    }
    
    /**
     * 获取设备ID
     * 
     * @return 设备ID
     */
    public String getDeviceId() {
        return deviceId;
    }
    
    /**
     * 设置设备ID
     * 
     * @param deviceId 设备ID
     * @return 当前配置对象
     */
    public SnmpConfig setDeviceId(String deviceId) {
        this.deviceId = deviceId;
        return this;
    }
    
    /**
     * 获取设备完整地址
     * 
     * @return 格式为 ip:port 的地址字符串
     */
    public String getFullAddress() {
        return ipAddress + ":" + port;
    }
    
    @Override
    public String toString() {
        return "SnmpConfig{" +
                "community='" + community + '\'' +
                ", version=" + version +
                ", retries=" + retries +
                ", timeout=" + timeout +
                ", ipAddress='" + ipAddress + '\'' +
                ", port=" + port +
                ", deviceId='" + deviceId + '\'' +
                '}';
    }
} 