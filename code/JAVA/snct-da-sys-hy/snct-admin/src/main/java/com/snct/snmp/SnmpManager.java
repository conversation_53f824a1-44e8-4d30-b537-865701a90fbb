package com.snct.snmp;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.snmp4j.CommunityTarget;
import org.snmp4j.Snmp;
import org.snmp4j.event.ResponseEvent;
import org.snmp4j.smi.Address;
import org.snmp4j.smi.GenericAddress;
import org.snmp4j.transport.DefaultUdpTransportMapping;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SNMP连接管理器
 * 管理SNMP连接池，使用单例模式
 * 
 * <AUTHOR>
 */
public class SnmpManager {
    
    private static final Logger logger = LoggerFactory.getLogger(SnmpManager.class);
    
    private static final SnmpManager INSTANCE = new SnmpManager();
    
    // SNMP连接缓存，key为设备地址（IP:端口）
    private final Map<String, Snmp> snmpConnections = new ConcurrentHashMap<>();
    
    // 设备目标缓存，key为设备地址（IP:端口）
    private final Map<String, CommunityTarget> targetCache = new ConcurrentHashMap<>();
    
    /**
     * 私有构造函数，防止外部实例化
     */
    private SnmpManager() {
        logger.info("初始化SNMP连接管理器");
    }
    
    /**
     * 获取单例实例
     * 
     * @return SnmpManager实例
     */
    public static SnmpManager getInstance() {
        return INSTANCE;
    }
    
    /**
     * 获取SNMP连接
     * 
     * @param config SNMP配置
     * @return SNMP连接
     */
    public synchronized Snmp getSnmpConnection(SnmpConfig config) {
        String connectionKey = config.getFullAddress();
        
        // 检查缓存中是否已存在连接
        Snmp snmp = snmpConnections.get(connectionKey);
        
        // 如果连接不存在或已关闭，则创建新连接
        if (snmp == null) {
            try {
                logger.debug("创建新的SNMP连接: {}", connectionKey);
                snmp = new Snmp(new DefaultUdpTransportMapping());
                snmp.listen();
                snmpConnections.put(connectionKey, snmp);
            } catch (IOException e) {
                logger.error("创建SNMP连接失败: {}", connectionKey, e);
                return null;
            }
        }
        
        return snmp;
    }
    
    /**
     * 获取设备SNMP目标
     * 
     * @param config SNMP配置
     * @return SNMP目标
     */
    public synchronized CommunityTarget getTarget(SnmpConfig config) {
        String targetKey = config.getFullAddress();
        
        // 检查缓存中是否已存在目标
        CommunityTarget target = targetCache.get(targetKey);
        
        // 如果目标不存在，则创建新目标
        if (target == null) {
            Address targetAddress = GenericAddress.parse("udp:" + config.getIpAddress() + "/" + config.getPort());
            
            target = new CommunityTarget();
            target.setCommunity(config.getCommunity());
            target.setVersion(config.getVersion());
            target.setAddress(targetAddress);
            target.setRetries(config.getRetries());
            target.setTimeout(config.getTimeout());
            
            targetCache.put(targetKey, target);
            logger.debug("创建新的SNMP目标: {}", targetKey);
        }
        
        return target;
    }
    
    /**
     * 执行SNMP请求
     * 
     * @param config SNMP配置
     * @param request SNMP请求
     * @return SNMP响应
     */
    public SnmpResponse execute(SnmpConfig config, SnmpRequest request) {
        if (config == null || request == null || !request.hasOids()) {
            logger.error("无效的SNMP请求参数");
            return null;
        }
        
        Snmp snmp = getSnmpConnection(config);
        CommunityTarget target = getTarget(config);
        
        if (snmp == null || target == null) {
            logger.error("获取SNMP连接或目标失败");
            return null;
        }
        
        try {
            // 创建PDU并发送请求
            logger.debug("发送SNMP请求到 {}, OID数量: {}", config.getFullAddress(), request.getOidCount());
            ResponseEvent response = snmp.send(request.createPdu(), target);
            
            if (response == null) {
                logger.error("SNMP请求响应为null");
                return null;
            }
            
            // 解析响应
            return new SnmpResponse(response.getResponse(), request);
        } catch (IOException e) {
            logger.error("SNMP请求执行失败: {}", config.getFullAddress(), e);
            return null;
        }
    }
    
    /**
     * 关闭特定设备的SNMP连接
     * 
     * @param config SNMP配置
     */
    public synchronized void closeConnection(SnmpConfig config) {
        String connectionKey = config.getFullAddress();
        Snmp snmp = snmpConnections.remove(connectionKey);
        
        if (snmp != null) {
            try {
                snmp.close();
                logger.debug("关闭SNMP连接: {}", connectionKey);
            } catch (IOException e) {
                logger.error("关闭SNMP连接异常: {}", connectionKey, e);
            }
        }
        
        // 同时移除目标缓存
        targetCache.remove(connectionKey);
    }
    
    /**
     * 关闭所有SNMP连接
     */
    public synchronized void closeAllConnections() {
        for (Map.Entry<String, Snmp> entry : snmpConnections.entrySet()) {
            try {
                entry.getValue().close();
                logger.debug("关闭SNMP连接: {}", entry.getKey());
            } catch (IOException e) {
                logger.error("关闭SNMP连接异常: {}", entry.getKey(), e);
            }
        }
        
        snmpConnections.clear();
        targetCache.clear();
        logger.info("已关闭所有SNMP连接");
    }
    
    /**
     * 获取当前活动连接数
     * 
     * @return 活动连接数
     */
    public int getActiveConnectionCount() {
        return snmpConnections.size();
    }
    
    /**
     * 获取当前连接设备的地址列表
     * 
     * @return 连接设备地址列表
     */
    public Map<String, String> getConnectionStatus() {
        Map<String, String> status = new HashMap<>();
        
        for (String key : snmpConnections.keySet()) {
            status.put(key, "连接中");
        }
        
        return status;
    }
} 