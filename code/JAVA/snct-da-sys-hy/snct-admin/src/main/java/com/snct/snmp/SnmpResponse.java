package com.snct.snmp;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.snmp4j.PDU;
import org.snmp4j.smi.VariableBinding;

import java.util.HashMap;
import java.util.Map;

/**
 * SNMP响应封装类
 * 用于解析SNMP响应PDU
 * 
 * <AUTHOR>
 */
public class SnmpResponse {
    
    private static final Logger logger = LoggerFactory.getLogger(SnmpResponse.class);
    
    // 响应状态码
    private final int errorStatus;
    
    // 响应状态信息
    private final String errorStatusText;
    
    // OID和响应值的映射
    private final Map<String, String> values = new HashMap<>();
    
    // 原始PDU
    private final PDU responsePdu;
    
    // 对应的请求
    private final SnmpRequest request;
    
    /**
     * 创建响应对象
     * 
     * @param responsePdu SNMP响应PDU
     * @param request 对应的请求对象
     */
    public SnmpResponse(PDU responsePdu, SnmpRequest request) {
        this.responsePdu = responsePdu;
        this.request = request;
        
        if (responsePdu != null) {
            this.errorStatus = responsePdu.getErrorStatus();
            this.errorStatusText = responsePdu.getErrorStatusText();
            
            if (errorStatus == 0) {
                parseResponse();
            } else {
                logger.error("SNMP请求错误: {}", errorStatusText);
            }
        } else {
            this.errorStatus = -1;
            this.errorStatusText = "响应PDU为空";
            logger.error("SNMP请求失败: 响应PDU为空");
        }
    }
    
    /**
     * 解析PDU响应
     */
    private void parseResponse() {
        if (responsePdu == null) {
            return;
        }
        
        for (int i = 0; i < responsePdu.size(); i++) {
            VariableBinding vb = responsePdu.get(i);
            String oid = vb.getOid().toString();
            String value = vb.getVariable().toString();
            
            values.put(oid, value);
        }
    }
    
    /**
     * 获取响应值
     * 
     * @param oid OID字符串
     * @return 响应值，如果不存在则返回null
     */
    public String getValue(String oid) {
        // 尝试不同格式的OID
        String value = null;
        
        // 1. 原始格式
        if (value == null) {
            value = values.get(oid);
        }
        
        // 2. 确保有前导点
        String oidWithDot = oid;
        if (!oidWithDot.startsWith(".")) {
            oidWithDot = "." + oid;
        }
        if (value == null) {
            value = values.get(oidWithDot);
        }
        
        // 3. 去除前导点
        String oidWithoutDot = oid;
        if (oidWithoutDot.startsWith(".")) {
            oidWithoutDot = oidWithoutDot.substring(1);
        }
        if (value == null) {
            value = values.get(oidWithoutDot);
        }
        
        // 如果仍然找不到，记录调试信息
        if (value == null) {
            logger.debug("未找到OID值，尝试过的OID格式: {}, {}, {}，可用的OID: {}", 
                    oid, oidWithDot, oidWithoutDot, values.keySet());
        }
        
        return value;
    }
    
    /**
     * 获取响应值并转换为Double
     * 
     * @param oid OID字符串
     * @param defaultValue 默认值
     * @return 转换后的Double值，转换失败则返回默认值
     */
    public Double getDoubleValue(String oid, Double defaultValue) {
        String value = getValue(oid);
        if (value == null) {
            return defaultValue;
        }
        
        try {
            return Double.parseDouble(value);
        } catch (NumberFormatException e) {
            logger.warn("无法转换为Double - OID: {}, 值: {}", oid, value);
            return defaultValue;
        }
    }
    
    /**
     * 获取响应值并转换为Long
     * 
     * @param oid OID字符串
     * @param defaultValue 默认值
     * @return 转换后的Long值，转换失败则返回默认值
     */
    public Long getLongValue(String oid, Long defaultValue) {
        String value = getValue(oid);
        if (value == null) {
            return defaultValue;
        }
        
        try {
            return Long.parseLong(value);
        } catch (NumberFormatException e) {
            logger.warn("无法转换为Long - OID: {}, 值: {}", oid, value);
            return defaultValue;
        }
    }
    
    /**
     * 获取响应值并转换为Integer
     * 
     * @param oid OID字符串
     * @param defaultValue 默认值
     * @return 转换后的Integer值，转换失败则返回默认值
     */
    public Integer getIntValue(String oid, Integer defaultValue) {
        String value = getValue(oid);
        if (value == null) {
            return defaultValue;
        }
        
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            logger.warn("无法转换为Integer - OID: {}, 值: {}", oid, value);
            return defaultValue;
        }
    }
    
    /**
     * 判断响应是否成功
     * 
     * @return 是否成功
     */
    public boolean isSuccess() {
        return errorStatus == 0 && responsePdu != null;
    }
    
    /**
     * 获取错误状态码
     * 
     * @return 错误状态码
     */
    public int getErrorStatus() {
        return errorStatus;
    }
    
    /**
     * 获取错误状态文本
     * 
     * @return 错误状态文本
     */
    public String getErrorStatusText() {
        return errorStatusText;
    }
    
    /**
     * 获取所有响应值
     * 
     * @return OID和值的映射
     */
    public Map<String, String> getAllValues() {
        return new HashMap<>(values);
    }
    
    /**
     * 获取响应大小
     * 
     * @return 响应中的变量数量
     */
    public int size() {
        return values.size();
    }
} 