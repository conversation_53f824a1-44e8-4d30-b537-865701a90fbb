package com.snct.snmp;

import com.snct.system.domain.Device;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * SNMP工具类
 * 提供静态方法方便使用SNMP功能
 * 
 * <AUTHOR>
 */
public class SnmpUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(SnmpUtils.class);
    
    // 默认SNMP端口
    private static final int DEFAULT_SNMP_PORT = 161;
    
    /**
     * 从设备对象创建SNMP配置
     * 
     * @param device 设备对象
     * @return SNMP配置
     */
    public static SnmpConfig createConfigFromDevice(Device device) {
        if (device == null || device.getIp() == null || device.getIp().isEmpty()) {
            logger.error("设备或设备IP为空，无法创建SNMP配置");
            return null;
        }
        
        int port = device.getPort() != null ? device.getPort() : DEFAULT_SNMP_PORT;
        
        return new SnmpConfig(device.getIp(), port, device.getId().toString())
                .setCommunity("public")
                .setVersion(1)
                .setRetries(2)
                .setTimeout(1500);
    }
    
    /**
     * 执行SNMP请求
     * 
     * @param device 设备对象
     * @param request SNMP请求
     * @return SNMP响应
     */
    public static SnmpResponse executeRequest(Device device, SnmpRequest request) {
        SnmpConfig config = createConfigFromDevice(device);
        if (config == null) {
            return null;
        }
        
        return executeRequest(config, request);
    }
    
    /**
     * 执行SNMP请求
     * 
     * @param config SNMP配置
     * @param request SNMP请求
     * @return SNMP响应
     */
    public static SnmpResponse executeRequest(SnmpConfig config, SnmpRequest request) {
        SnmpManager manager = SnmpManager.getInstance();
        return manager.execute(config, request);
    }
    
    /**
     * 执行简单的SNMP Get请求
     * 
     * @param device 设备对象
     * @param oids 需要查询的OID数组
     * @return SNMP响应
     */
    public static SnmpResponse get(Device device, String... oids) {
        SnmpConfig config = createConfigFromDevice(device);
        if (config == null) {
            return null;
        }
        
        SnmpRequest request = new SnmpRequest(oids);
        return executeRequest(config, request);
    }
    
    /**
     * 关闭设备的SNMP连接
     * 
     * @param device 设备对象
     */
    public static void closeConnection(Device device) {
        SnmpConfig config = createConfigFromDevice(device);
        if (config != null) {
            SnmpManager.getInstance().closeConnection(config);
        }
    }
    
    /**
     * 关闭所有SNMP连接
     */
    public static void closeAllConnections() {
        SnmpManager.getInstance().closeAllConnections();
    }
    
    /**
     * 获取当前活动连接数
     * 
     * @return 活动连接数
     */
    public static int getActiveConnectionCount() {
        return SnmpManager.getInstance().getActiveConnectionCount();
    }
} 