package com.snct.utils;

import com.snct.common.config.SnctConfig;
import com.snct.system.domain.Device;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 设备原始数据文件保存工具类
 * 
 * 提供设备原始数据的文件保存功能，支持按设备编码和日期分类存储
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
public class DeviceRawDataFileUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(DeviceRawDataFileUtil.class);
    
    /**
     * 保存原始数据到本地文件
     * 根据设备编码和日期分类，保存为txt文本格式，包含采集时间和原始数据
     *
     * @param device    设备对象
     * @param rawData   原始数据
     * @param timestamp 采集时间戳
     */
    public static void saveRawDataToFile(Device device, String rawData, Long timestamp) {
        if (device == null || StringUtils.isBlank(device.getCode()) || StringUtils.isBlank(rawData)) {
            return;
        }

        try {
            String deviceCode = device.getCode();
            Date date = new Date(timestamp);
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
            SimpleDateFormat timeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

            String dateStr = dateFormat.format(date);
            String timeStr = timeFormat.format(date);

            // 构建文件路径：基础路径/原始数据/设备编码/年月日/
            String basePath = SnctConfig.getProfile();
            String dirPath = basePath + File.separator + "device_raw_data" + File.separator + deviceCode + File.separator + dateStr;

            // 创建目录
            File dir = new File(dirPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 构建文件名：设备编码_年月日.txt
            String fileName = deviceCode + "_" + dateStr + ".txt";
            File file = new File(dir, fileName);

            // 构建文件内容：[时间戳] 原始数据内容
            String content = "[" + timeStr + "] " + rawData + System.lineSeparator();

            // 追加写入文件
            try (FileWriter writer = new FileWriter(file, true)) {
                writer.write(content);
                writer.flush();
            }

            logger.debug("原始数据已保存到文件: {}", file.getAbsolutePath());

        } catch (IOException e) {
            logger.error("保存设备[{}]原始数据到文件失败", device.getCode(), e);
        } catch (Exception e) {
            logger.error("保存设备[{}]原始数据时发生异常", device.getCode(), e);
        }
    }
}
