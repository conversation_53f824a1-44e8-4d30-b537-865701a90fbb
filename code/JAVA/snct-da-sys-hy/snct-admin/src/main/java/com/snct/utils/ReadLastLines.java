package com.snct.utils;

import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;

/**
 *
 * 读取日志文件内容工具
 * **/

public class ReadLastLines {
    public static void main(String[] args) throws IOException {
        String filePath = "E:\\nginx.conf";
        int numLines = 10;
        readLastNLines(filePath, numLines);
    }

    /**
     *
     * 读取最近多少行数据
     * **/
    public static String readLastLine(String filePath, int numLines) throws IOException {
        RandomAccessFile file = new RandomAccessFile(filePath, "r");
        FileChannel channel = file.getChannel();
        long fileSize = channel.size();
        long bufferSize = 1024; // 缓冲区大小，可以根据需要调整
        long position = 0;
        int lineCount = 0;
        StringBuilder lastLines = new StringBuilder();
        boolean endOfFile = false;

        while (!endOfFile) {
            position = Math.max(0, fileSize - bufferSize);
            channel.position(position);
            MappedByteBuffer buffer = channel.map(FileChannel.MapMode.READ_ONLY, position, (fileSize - position));
            byte[] bytes = new byte[(int) (fileSize - position)];
            buffer.get(bytes);
            String content = new String(bytes);
            String[] lines = content.split("\n");
            for (int i = lines.length - 1; i >= 0 && lineCount < numLines; i--) {
                lastLines.insert(0, lines[i] + "\n");
                lineCount++;
            }
            if (position == 0) {
                endOfFile = true;
            } else {
                fileSize = position; // 缩小下一次搜索的范围
            }
        }
        System.out.println(lastLines.toString());
        file.close();
        return lastLines.toString();
    }


    /**
     *
     * 读取最近多少行数据
     * **/
    public static void readLastNLines(String filePath, int numLines) throws IOException {
        RandomAccessFile file = new RandomAccessFile(filePath, "r");
        FileChannel channel = file.getChannel();
        long fileSize = channel.size();
        long bufferSize = 1024; // 缓冲区大小，可以根据需要调整
        long position = 0;
        int lineCount = 0;
        StringBuilder lastLines = new StringBuilder();
        boolean endOfFile = false;
 
        while (!endOfFile) {
            position = Math.max(0, fileSize - bufferSize);
            channel.position(position);
            MappedByteBuffer buffer = channel.map(FileChannel.MapMode.READ_ONLY, position, (fileSize - position));
            byte[] bytes = new byte[(int) (fileSize - position)];
            buffer.get(bytes);
            String content = new String(bytes);
            String[] lines = content.split("\n");
            for (int i = lines.length - 1; i >= 0 && lineCount < numLines; i--) {
                lastLines.insert(0, lines[i] + "\n");
                lineCount++;
            }
            if (position == 0) {
                endOfFile = true;
            } else {
                fileSize = position; // 缩小下一次搜索的范围
            }
        }
        System.out.println(lastLines.toString());
        file.close();
    }
}