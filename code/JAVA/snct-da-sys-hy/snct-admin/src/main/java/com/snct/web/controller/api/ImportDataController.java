package com.snct.web.controller.api;

import com.alibaba.fastjson2.JSONObject;
import com.snct.analysis.vo.AttitudeHbaseVo1;
import com.snct.common.annotation.Anonymous;
import com.snct.common.constant.CacheConstants;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.core.redis.RedisCache;
import com.snct.common.utils.DateUtils;
import com.snct.common.utils.StringUtils;
import com.snct.common.utils.ip.IpUtils;
import com.snct.device.manager.DeviceConnectionManager;
import com.snct.system.domain.Device;
import com.snct.system.domain.data.BuDataAttitude;
import com.snct.system.service.IDeviceService;
import com.snct.system.service.impl.BuDataAttitudeServiceImpl;
import com.snct.utils.DeviceRawDataFileUtil;
import com.snct.web.controller.api.dto.*;
import com.snct.web.controller.business.DeviceController;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 设备数据传输API控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/device")
public class ImportDataController {
    private static final Logger log = LoggerFactory.getLogger(ImportDataController.class);

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private IDeviceService deviceService;

    @Autowired
    private DeviceConnectionManager connectionManager;

    @Autowired
    private BuDataAttitudeServiceImpl buDataAttitudeService;

    /**
     * 设备认证固定账号
     */
    private static final String DEVICE_ACCOUNT = "attitude";

    /**
     * 设备认证固定密码
     */
    private static final String DEVICE_PASSWORD = "Aa123456#";

    /**
     * Token有效期（8小时，单位：秒）
     */
    private static final int TOKEN_EXPIRE_TIME = 8 * 60 * 60;

    /**
     * JWT密钥
     */
    @Value("${token.secret}")
    private String jwtSecret;

    private static final String ATTITUDE_DATA = CacheConstants.DEVICE_DATA_KEY + "attitude";

    /**
     * 姿态设备固定编码
     */
    private static final String ATTITUDE_DEVICE_CODE = "033A";

    /**
     * 设备登录认证接口
     *
     * @param request 登录请求
     * @return 登录响应
     */
    @Anonymous
    @PostMapping("/loginApi")
    public AjaxResult deviceLogin(@Validated @RequestBody DeviceLoginRequest request, HttpServletRequest httpRequest) {
        try {

            // 验证设备账号和密码
            if (!DEVICE_ACCOUNT.equals(request.getDeviceAccount()) ||
                    !DEVICE_PASSWORD.equals(request.getDevicePassword())) {
                log.warn("设备登录失败，账号或密码错误: {}", request.getDeviceAccount());
                return AjaxResult.error("设备账号或密码错误");
            }

            // 生成token
            String token = generateDeviceToken(request.getDeviceAccount());

            // 计算过期时间
            long currentTime = System.currentTimeMillis();
            long expireTime = currentTime + TOKEN_EXPIRE_TIME * 1000L;

            // 获取客户端IP
            String ipAddress = IpUtils.getIpAddr(httpRequest);

            // 创建token信息
            DeviceTokenInfo tokenInfo = new DeviceTokenInfo(
                    request.getDeviceAccount(),
                    currentTime,
                    expireTime,
                    ipAddress
            );

            // 存储到Redis
            String tokenKey = CacheConstants.DEVICE_TOKEN_KEY + token;
            redisCache.setCacheObject(tokenKey, tokenInfo, TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);

            // 构建响应
            DeviceLoginResponse response = new DeviceLoginResponse(token, expireTime, TOKEN_EXPIRE_TIME);

            //log.info("设备登录成功，账号: {}, token: {}, 过期时间: {}", request.getDeviceAccount(), token, expireTime);
            log.info("姿态登录成功>: {}", request.getDeviceAccount());

            return AjaxResult.success( "登录成功", response);
        } catch (Exception e) {
            log.error("设备登录异常", e);
            return AjaxResult.error("登录失败，系统异常");
        }
    }

    /**
     * 设备数据接收接口
     *
     * @param request     姿态数据请求
     * @param httpRequest HTTP请求
     * @return 处理结果
     */
    @Anonymous
    @PostMapping("/importData")
    public AjaxResult receiveAttitudeData(@Validated @RequestBody DeviceDataRequest request,
                                          HttpServletRequest httpRequest) {
        try {
            log.info("接收姿态设备[{}]数据请求: {}", ATTITUDE_DEVICE_CODE, request);

            // 获取并验证token
            String token = getTokenFromRequest(httpRequest);
            if (StringUtils.isEmpty(token)) {
                updateDeviceConnectionStatus(ATTITUDE_DEVICE_CODE,
                    DeviceConnectionManager.ConnectionStatus.DISCONNECTED, "缺少token");
                log.warn("数据接收失败，缺少token");
                return AjaxResult.error(401, "缺少访问令牌");
            }

            // 验证token有效性
            DeviceTokenInfo tokenInfo = validateDeviceToken(token);
            if (tokenInfo == null) {
                updateDeviceConnectionStatus(ATTITUDE_DEVICE_CODE,
                    DeviceConnectionManager.ConnectionStatus.DISCONNECTED, "token无效");
                log.warn("数据接收失败，token无效: {}", token);
                return AjaxResult.error(401, "访问令牌无效");
            }

            if (tokenInfo.isExpired()) {
                updateDeviceConnectionStatus(ATTITUDE_DEVICE_CODE,
                    DeviceConnectionManager.ConnectionStatus.DISCONNECTED, "token过期");
                log.warn("数据接收失败，token已过期: {}", token);
                return AjaxResult.error(401, "访问令牌已过期");
            }

            // 设置数据时间戳（如果未提供）
            if (request.getTimestamp() == null) {
                request.setTimestamp(System.currentTimeMillis());
            }

            // 处理姿态数据
            Map<String, Object> properties = request.getProperties();
            AttitudeHbaseVo1 attitudeHbaseVo1 = new AttitudeHbaseVo1();
            long currentTime = request.getTimestamp() == null ? System.currentTimeMillis() :
                    request.getTimestamp().longValue();
            attitudeHbaseVo1.setInitialTime(DateUtils.fetchWholeSecond(Long.valueOf(currentTime)).toString());
            attitudeHbaseVo1.setInitialBjTime(DateUtils.parseTimeToDate(System.currentTimeMillis(), "yyyy-MM-dd " +
                    "HH:mm:ss").toString());
            attitudeHbaseVo1.setRolling(properties.get("rolling") != null ? properties.get("rolling").toString() :
                    null);
            attitudeHbaseVo1.setPitch(properties.get("pitch") != null ? properties.get("pitch").toString() : null);
            attitudeHbaseVo1.setHeading(properties.get("heading") != null ? properties.get("heading").toString() :
                    null);
            String lonValue = properties.get("lon") != null ? properties.get("lon").toString() :
                    (properties.get("lng") != null ? properties.get("lng").toString() : null);
            attitudeHbaseVo1.setLon(lonValue);
            attitudeHbaseVo1.setLat(properties.get("lat") != null ? properties.get("lat").toString() : null);
            attitudeHbaseVo1.setHeight(properties.get("height") != null ? properties.get("height").toString() : null);
            attitudeHbaseVo1.setDistance(properties.get("distance") != null ? properties.get("distance").toString() : null);

            String message = JSONObject.toJSONString(properties);

            Device device = getDeviceByCode(ATTITUDE_DEVICE_CODE);
            try {
                // 判断该设备是否开启了预览功能
                if (DeviceController.viewKey != null && DeviceController.viewKey.containsKey(ATTITUDE_DEVICE_CODE)) {
                    // 获取当前时间并格式化
                    LocalDateTime now = LocalDateTime.now();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
                    String formattedTime = now.format(formatter);
                    // 构建key: 设备编码###时间
                    String key = ATTITUDE_DEVICE_CODE + "###" + formattedTime;

                    // 存放原始数据到viewValue
                    DeviceController.viewValue.put(key, message);
                }
            } catch (Exception e) {
                log.error("保存[{}]预览数据异常", ATTITUDE_DEVICE_CODE, e);
            }

            // 保存原数据
            DeviceRawDataFileUtil.saveRawDataToFile(device, message, request.getTimestamp());

            // 存储到Redis
            redisCache.setCacheObject(ATTITUDE_DATA, attitudeHbaseVo1);

            // 保存数据到数据库
            BuDataAttitude buDataAttitude = new BuDataAttitude();
            buDataAttitude.setDeviceId(Math.toIntExact(device.getId()));
            buDataAttitude.setDeviceCode(device.getCode());
            buDataAttitude.setDeviceName(device.getName());
            buDataAttitude.setInitialTime(request.getTimestamp());
            buDataAttitude.setInitialBjTime(new Date(request.getTimestamp()));
            if (StringUtils.isNotEmpty(attitudeHbaseVo1.getLon())) {
                buDataAttitude.setLon(attitudeHbaseVo1.getLon());
            }
            if (StringUtils.isNotEmpty(attitudeHbaseVo1.getLat())) {
                buDataAttitude.setLat(attitudeHbaseVo1.getLat());
            }
            if (StringUtils.isNotEmpty(attitudeHbaseVo1.getHeading())) {
                buDataAttitude.setHeading(attitudeHbaseVo1.getHeading());
            }
            if (StringUtils.isNotEmpty(attitudeHbaseVo1.getPitch())) {
                buDataAttitude.setPitch(attitudeHbaseVo1.getPitch());
            }
            if (StringUtils.isNotEmpty(attitudeHbaseVo1.getRolling())) {
                buDataAttitude.setRolling(attitudeHbaseVo1.getRolling());
            }
            if (StringUtils.isNotEmpty(attitudeHbaseVo1.getDistance())) {
                buDataAttitude.setDistance(attitudeHbaseVo1.getDistance());
            }
            if (StringUtils.isNotEmpty(attitudeHbaseVo1.getHeight())) {
                buDataAttitude.setHeight(attitudeHbaseVo1.getHeight());
            }
            buDataAttitudeService.insertBuDataAttitude(buDataAttitude);

            // 更新设备连接状态为连接
            updateDeviceConnectionStatus(ATTITUDE_DEVICE_CODE,
                DeviceConnectionManager.ConnectionStatus.CONNECTED, "数据接收成功");

            //log.info("设备[{}]数据接收成功，存储到Redis key: {}", ATTITUDE_DEVICE_CODE, ATTITUDE_DATA);
            return AjaxResult.success("姿态数据接收成功");
        } catch (Exception e) {
            updateDeviceConnectionStatus(ATTITUDE_DEVICE_CODE,
                DeviceConnectionManager.ConnectionStatus.UNKNOWN, "系统异常: " + e.getMessage());
            log.error("设备[{}]数据接收异常", ATTITUDE_DEVICE_CODE, e);
            return AjaxResult.error("姿态数据接收失败，系统异常");
        }
    }

    /**
     * 生成设备Token
     *
     * @param deviceAccount 设备账号
     * @return JWT Token
     */
    private String generateDeviceToken(String deviceAccount) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("deviceAccount", deviceAccount);
        claims.put("tokenType", "device");
        claims.put("createTime", System.currentTimeMillis());

        return Jwts.builder()
                .setClaims(claims)
                .setSubject(deviceAccount)
                .signWith(SignatureAlgorithm.HS512, jwtSecret)
                .compact();
    }

    /**
     * 从请求中获取Token
     *
     * @param request HTTP请求
     * @return Token字符串
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        // 优先从Header中获取
        String token = request.getHeader("Authorization");
        if (StringUtils.isNotEmpty(token)) {
            if (token.startsWith("Bearer ")) {
                return token.substring(7);
            }
            return token;
        }

        // 从参数中获取
        token = request.getParameter("token");
        return token;
    }

    /**
     * 验证设备Token
     *
     * @param token Token字符串
     * @return Token信息，如果无效返回null
     */
    private DeviceTokenInfo validateDeviceToken(String token) {
        try {
            // 验证JWT格式和签名
            Claims claims = Jwts.parser()
                    .setSigningKey(jwtSecret)
                    .parseClaimsJws(token)
                    .getBody();

            // 检查token类型
            String tokenType = (String) claims.get("tokenType");
            if (!"device".equals(tokenType)) {
                log.warn("Token类型不匹配: {}", tokenType);
                return null;
            }

            // 从Redis中获取token信息
            String tokenKey = CacheConstants.DEVICE_TOKEN_KEY + token;
            DeviceTokenInfo tokenInfo = redisCache.getCacheObject(tokenKey);

            if (tokenInfo == null) {
                log.warn("Token在Redis中不存在: {}", token);
                return null;
            }

            return tokenInfo;
        } catch (Exception e) {
            log.warn("Token验证失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 查询设备数据接口（用于调试）
     *
     * @param httpRequest HTTP请求
     * @return 设备数据
     */
    @Anonymous
    @GetMapping("/data")
    public AjaxResult getDeviceData(HttpServletRequest httpRequest) {
        try {
            // 获取并验证token
            String token = getTokenFromRequest(httpRequest);
            if (StringUtils.isEmpty(token)) {
                return AjaxResult.error(401, "缺少访问令牌");
            }

            DeviceTokenInfo tokenInfo = validateDeviceToken(token);
            if (tokenInfo == null || tokenInfo.isExpired()) {
                return AjaxResult.error(401, "访问令牌无效或已过期");
            }

            // 获取姿态数据
            Object attitudeData = redisCache.getCacheObject(ATTITUDE_DATA);

            if (attitudeData == null) {
                return AjaxResult.error("数据不存在");
            }

            return AjaxResult.success("查询成功", attitudeData);
        } catch (Exception e) {
            log.error("查询数据异常", e);
            return AjaxResult.error("查询失败，系统异常");
        }
    }

    /**
     * 更新设备连接状态的辅助方法
     *
     * @param deviceCode 设备编码
     * @param status 连接状态
     * @param reason 状态变更原因
     */
    private void updateDeviceConnectionStatus(String deviceCode,
                                            DeviceConnectionManager.ConnectionStatus status,
                                            String reason) {
        try {
            Device device = getDeviceByCode(deviceCode);
            if (device != null) {
                connectionManager.updateDeviceConnectionStatus(device, status,
                    DeviceConnectionManager.ProtocolType.HTTP);
                log.info("设备[{}]连接状态更新: {} - {}", deviceCode, status.getDescription(), reason);
            }
        } catch (Exception e) {
            log.error("更新设备[{}]连接状态失败", deviceCode, e);
        }
    }

    /**
     * 根据设备编码获取设备对象
     *
     * @param deviceCode 设备编码
     * @return 设备对象
     */
    private Device getDeviceByCode(String deviceCode) {
        try {
            Device query = new Device();
            query.setCode(deviceCode);
            query.setEnable(1);
            List<Device> devices = deviceService.selectDeviceList(query);

            if (devices != null && !devices.isEmpty()) {
                return devices.get(0);
            }
        } catch (Exception e) {
            log.error("根据设备编码[{}]查询设备失败", deviceCode, e);
        }
        return null;
    }


}

