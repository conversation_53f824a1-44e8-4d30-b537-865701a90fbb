package com.snct.web.controller.api.dto;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import java.util.HashMap;
import java.util.Map;

/**
 * 设备数据接收请求DTO
 * 
 * <AUTHOR>
 */
public class DeviceDataRequest
{
    /**
     * 动态接收所有字段的Map
     */
    private Map<String, Object> properties = new HashMap<>();

    /**
     * 数据时间戳
     */
    private Long timestamp;

    /**
     * 使用Jackson的@JsonAnySetter注解来动态接收所有字段
     */
    @JsonAnySetter
    public void setProperty(String key, Object value) {
        // 如果是timestamp字段，单独处理
        if ("timestamp".equals(key) && value != null) {
            if (value instanceof Number) {
                this.timestamp = ((Number) value).longValue();
            }
        } else {
            this.properties.put(key, value);
        }
    }

    /**
     * 获取所有动态属性
     */
    public Map<String, Object> getProperties() {
        return properties;
    }

    /**
     * 获取指定属性值
     */
    public Object getProperty(String key) {
        return properties.get(key);
    }

    public Long getTimestamp()
    {
        return timestamp;
    }

    public void setTimestamp(Long timestamp)
    {
        this.timestamp = timestamp;
    }

    @Override
    public String toString()
    {
        return "DeviceDataRequest{" +
                "properties=" + properties +
                ", timestamp=" + timestamp +
                '}';
    }
}
