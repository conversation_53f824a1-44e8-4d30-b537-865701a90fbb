package com.snct.web.controller.api.dto;

/**
 * 设备登录响应DTO
 * 
 * <AUTHOR>
 */
public class DeviceLoginResponse
{
    /**
     * 访问令牌
     */
    private String token;

    /**
     * 令牌过期时间（毫秒时间戳）
     */
    private Long expireTime;

    /**
     * 令牌有效期（秒）
     */
    private Integer expiresIn;

    public DeviceLoginResponse()
    {
    }

    public DeviceLoginResponse(String token, Long expireTime, Integer expiresIn)
    {
        this.token = token;
        this.expireTime = expireTime;
        this.expiresIn = expiresIn;
    }

    public String getToken()
    {
        return token;
    }

    public void setToken(String token)
    {
        this.token = token;
    }

    public Long getExpireTime()
    {
        return expireTime;
    }

    public void setExpireTime(Long expireTime)
    {
        this.expireTime = expireTime;
    }

    public Integer getExpiresIn()
    {
        return expiresIn;
    }

    public void setExpiresIn(Integer expiresIn)
    {
        this.expiresIn = expiresIn;
    }

    @Override
    public String toString()
    {
        return "DeviceLoginResponse{" +
                "token='" + token + '\'' +
                ", expireTime=" + expireTime +
                ", expiresIn=" + expiresIn +
                '}';
    }
}
