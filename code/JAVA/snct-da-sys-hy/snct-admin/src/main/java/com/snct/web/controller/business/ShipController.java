package com.snct.web.controller.business;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.snct.common.enums.KafkaMsgType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.snct.common.annotation.Log;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.enums.BusinessType;
import com.snct.system.domain.Ship;
import com.snct.system.service.IShipService;
import com.snct.common.utils.poi.ExcelUtil;
import com.snct.common.core.page.TableDataInfo;
import com.snct.kafka.KafkaMessage;
import com.snct.kafka.KafkaService;
import com.alibaba.fastjson2.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 船只Controller
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@RestController
@RequestMapping("/business/ship")
public class ShipController extends BaseController
{
    private static final Logger logger = LoggerFactory.getLogger(ShipController.class);
    
    @Autowired
    private IShipService shipService;
    
    @Autowired
    private KafkaService kafkaService;

    /**
     * 查询船只列表
     */
    @PreAuthorize("@ss.hasPermi('business:ship:list')")
    @GetMapping("/list")
    public TableDataInfo list(Ship ship)
    {
        startPage();
        List<Ship> list = shipService.selectShipList(ship);
        return getDataTable(list);
    }

    /**
     * 导出船只列表
     */
    @PreAuthorize("@ss.hasPermi('business:ship:export')")
    @Log(title = "船只", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Ship ship)
    {
        List<Ship> list = shipService.selectShipList(ship);
        ExcelUtil<Ship> util = new ExcelUtil<Ship>(Ship.class);
        util.exportExcel(response, list, "船只数据");
    }

    /**
     * 获取船只详细信息
     */
    @PreAuthorize("@ss.hasPermi('business:ship:query')")
    @GetMapping(value = "/{shipId}")
    public AjaxResult getInfo(@PathVariable("shipId") Long shipId)
    {
        return success(shipService.selectShipByShipId(shipId));
    }

    /**
     * 新增船只
     */
    @PreAuthorize("@ss.hasPermi('business:ship:add')")
    @Log(title = "船只", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Ship ship)
    {
        List list = shipService.selectAllShipList();
        if(list.size()>0) {
            return error("船舶信息已经存在");
        }
        if(  !"0".equals( ship.getStatus()+"" )  && !"1".equals( ship.getStatus()+"" )  ) {
            //未选择  默认未启用
            ship.setStatus(0);
        }
        return toAjax(shipService.insertShip(ship));
    }

    /**
     * 修改船只
     */
    @PreAuthorize("@ss.hasPermi('business:ship:edit')")
    @Log(title = "船只", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Ship ship)
    {
        return toAjax(shipService.updateShip(ship));
    }

    /**
     * 删除船只
     */
    @PreAuthorize("@ss.hasPermi('business:ship:remove')")
    @Log(title = "船只", businessType = BusinessType.DELETE)
	@DeleteMapping("/{shipIds}")
    public AjaxResult remove(@PathVariable Long[] shipIds)
    {
        return toAjax(shipService.deleteShipByShipIds(shipIds));
    }

    /**
     * 同步船舶消息
     */
    @PreAuthorize("@ss.hasPermi('business:ship:edit')")
    @Log(title = "船只", businessType = BusinessType.OTHER)
    @PostMapping("/sync")
    public AjaxResult syncShipMessage()
    {
        try {
            List<Ship> ships = shipService.selectAllShipList();
            if (ships != null && !ships.isEmpty()) {
                Ship ship = ships.get(0);
                KafkaMessage msg = new KafkaMessage();
                msg.setType(KafkaMsgType.SYC.ordinal());
                msg.setCode("SHIP_DATA");
                msg.setMsg(JSONObject.toJSONString(ship));
                kafkaService.send2Kafka(msg);
                logger.info("同步船舶消息成功：{}", ship.toString());
                return success("同步船舶消息成功");
            } else {
                logger.warn("没有获取到船舶数据");
                return error("没有获取到船舶数据");
            }
        } catch (Exception e) {
            logger.error("同步船舶消息失败", e);
            return error("同步船舶消息失败：" + e.getMessage());
        }
    }
}
