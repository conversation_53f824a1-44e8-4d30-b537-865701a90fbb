package com.snct.web.controller.monitor;

import com.snct.common.annotation.Log;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.core.page.TableDataInfo;
import com.snct.common.enums.BusinessType;
import com.snct.common.utils.DateUtils;
import com.snct.common.utils.poi.ExcelUtil;
import com.snct.system.domain.SysOperLog;
import com.snct.system.service.ISysOperLogService;
import com.snct.utils.ReadLastLines;
import com.snct.utils.SysCmd;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 操作日志记录
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/sysLog")
public class SysLogController extends BaseController
{

    @Autowired
    private SysCmd sysCmd;

    static int t = 0;
    @PreAuthorize("@ss.hasPermi('monitor:syslog:info')")
    @PostMapping("/info")
    public AjaxResult info() {
        try{
            int now = ( (int) (System.currentTimeMillis()/1000) );
            if(  now  - t >= 5 ){  //5秒
                t = now;
                String date = DateUtils.parseTimeToDate(System.currentTimeMillis(),"yyyy-MM-dd").toString();
                String filePath = "/snct/logs/nohup.out";
                //String filePath = "E:\\nginx.conf";
                int numLines = 120;
                String text = ReadLastLines.readLastLine(filePath, numLines);
                Map<String, Object> result = new HashMap<>();
                result.put("info", text.replace("\n","<br/>")+"<br/><br/><br/>");
                return AjaxResult.success(result);
            }else{
                Map<String, Object> result = new HashMap<>();
                result.put("info", "info");
                return AjaxResult.success(result);
            }
        }catch (IOException e){
            return AjaxResult.error("读取日志失败。");
        }
    }

    @PreAuthorize("@ss.hasPermi('monitor:syslog:remove')")
    @DeleteMapping("/clean")
    public AjaxResult clean()
    {
        //sysCmd.cmd("");
        return success();
    }
}
