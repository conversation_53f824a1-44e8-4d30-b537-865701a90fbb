package com.snct.system.domain;

/**
 * 网卡配置
 * <AUTHOR>
 * @date 2025-06-23
 */
public class NetworkConfig {
    /** 网卡名称 */
    private String name;
    /** 网卡类型 */
    private String type;
    /** 网络模式(如static/dhcp) */
    private String mode;
    /** IP地址 */
    private String ip;
    /** 子网掩码 */
    private String netmask;
    /** 网关地址 */
    private String gateway;
    /** 首选DNS服务器 */
    private String dns1;
    /** 备用DNS服务器 */
    private String dns2;
    /** 访问地址1 */
    private String visit1;
    /** 访问地址2 */
    private String visit2;
    /** 访问地址3 */
    private String visit3;

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getIp() {
        return this.ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getNetmask() {
        return this.netmask;
    }

    public void setNetmask(String netmask) {
        this.netmask = netmask;
    }

    public String getGateway() {
        return this.gateway;
    }

    public void setGateway(String gateway) {
        this.gateway = gateway;
    }

    public String getMode() {
        return this.mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getDns1() {
        return this.dns1;
    }

    public void setDns1(String dns1) {
        this.dns1 = dns1;
    }

    public String getDns2() {
        return this.dns2;
    }

    public void setDns2(String dns2) {
        this.dns2 = dns2;
    }

    public String getVisit1() {
        return this.visit1;
    }

    public void setVisit1(String visit1) {
        this.visit1 = visit1;
    }

    public String getVisit2() {
        return this.visit2;
    }

    public void setVisit2(String visit2) {
        this.visit2 = visit2;
    }

    public String getVisit3() {
        return this.visit3;
    }

    public void setVisit3(String visit3) {
        this.visit3 = visit3;
    }
}