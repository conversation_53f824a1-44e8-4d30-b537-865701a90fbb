package com.snct.system.domain.data;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;

/**
 * 功放设备数据对象 bu_data_amplifier
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public class BuDataAmplifier extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 设备ID
     */
    @Excel(name = "设备ID")
    private Integer deviceId;

    /**
     * 设备编号
     */
    @Excel(name = "设备编号")
    private String deviceCode;

    /**
     * 设备名称
     */
    @Excel(name = "设备名称")
    private String deviceName;

    /**
     * 数据采集时间戳
     */
    @Excel(name = "数据采集时间戳")
    private Long initialTime;

    /**
     * 北京时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "北京时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date initialBjTime;

    /**
     * 数据状态(0-正常,1-异常)
     */
    @Excel(name = "数据状态(0-正常,1-异常)")
    private Integer status;

    /**
     * 衰减值
     */
    @Excel(name = "衰减值")
    private BigDecimal decay;

    /**
     * 温度(℃)
     */
    @Excel(name = "温度(℃)")
    private BigDecimal temperature;

    /**
     * 输出功率
     */
    @Excel(name = "输出功率")
    private BigDecimal outputPower;

    /**
     * 设备工作状态
     */
    @Excel(name = "设备工作状态")
    private Integer deviceStatus;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setDeviceId(Integer deviceId) {
        this.deviceId = deviceId;
    }

    public Integer getDeviceId() {
        return deviceId;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setInitialTime(Long initialTime) {
        this.initialTime = initialTime;
    }

    public Long getInitialTime() {
        return initialTime;
    }

    public void setInitialBjTime(Date initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public Date getInitialBjTime() {
        return initialBjTime;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public void setDecay(BigDecimal decay) {
        this.decay = decay;
    }

    public BigDecimal getDecay() {
        return decay;
    }

    public void setTemperature(BigDecimal temperature) {
        this.temperature = temperature;
    }

    public BigDecimal getTemperature() {
        return temperature;
    }

    public void setOutputPower(BigDecimal outputPower) {
        this.outputPower = outputPower;
    }

    public BigDecimal getOutputPower() {
        return outputPower;
    }

    public void setDeviceStatus(Integer deviceStatus) {
        this.deviceStatus = deviceStatus;
    }

    public Integer getDeviceStatus() {
        return deviceStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("deviceId", getDeviceId())
                .append("deviceCode", getDeviceCode())
                .append("deviceName", getDeviceName())
                .append("initialTime", getInitialTime())
                .append("initialBjTime", getInitialBjTime())
                .append("status", getStatus())
                .append("decay", getDecay())
                .append("temperature", getTemperature())
                .append("outputPower", getOutputPower())
                .append("deviceStatus", getDeviceStatus())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
