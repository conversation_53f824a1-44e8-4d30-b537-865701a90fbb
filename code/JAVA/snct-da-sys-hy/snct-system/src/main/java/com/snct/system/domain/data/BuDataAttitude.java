package com.snct.system.domain.data;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;

/**
 * 姿态设备数据对象 bu_data_attitude
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class BuDataAttitude extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 设备ID */
    @Excel(name = "设备ID")
    private Integer deviceId;

    /** 设备编号 */
    @Excel(name = "设备编号")
    private String deviceCode;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String deviceName;

    /** 数据采集时间戳 */
    @Excel(name = "数据采集时间戳")
    private Long initialTime;

    /** 北京时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "北京时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date initialBjTime;

    /** 数据状态(0-正常,1-异常) */
    @Excel(name = "数据状态(0-正常,1-异常)")
    private Integer status;

    /** UTC时间(时/分/秒/小数秒) */
    @Excel(name = "UTC时间(时/分/秒/小数秒)")
    private String utc;

    /** 纬度 */
    @Excel(name = "纬度")
    private String lat;

    /** 经度 */
    @Excel(name = "经度")
    private String lon;

    /** 航向角 */
    @Excel(name = "航向角")
    private String heading;

    /** 横滚角 */
    @Excel(name = "横滚角")
    private String rolling;

    /** 俯仰角 */
    @Excel(name = "俯仰角")
    private String pitch;

    /** 高度 */
    @Excel(name = "高度")
    private String height;

    /** 椭球高 */
    @Excel(name = "椭球高")
    private String elpHeight;

    /** 距离 */
    @Excel(name = "距离")
    private String distance;

    /** 主导和从站内之间的距离(双天线基线长) */
    @Excel(name = "主导和从站内之间的距离(双天线基线长)")
    private String baselineLength;

    /** 北方向速度 */
    @Excel(name = "北方向速度")
    private String velN;

    /** 东方向速度 */
    @Excel(name = "东方向速度")
    private String velE;

    /** 地向速度 */
    @Excel(name = "地向速度")
    private String velD;

    /** 地面速度 */
    @Excel(name = "地面速度")
    private String velG;

    /** 高精度坐标北向X轴 */
    @Excel(name = "高精度坐标北向X轴")
    private String coordinateNorthing;

    /** 高精度坐标东向Y轴 */
    @Excel(name = "高精度坐标东向Y轴")
    private String coordinateEasting;

    /** 基站坐标下的移动站X坐标(北距离) */
    @Excel(name = "基站坐标下的移动站X坐标(北距离)")
    private String northDistance;

    /** 基站坐标下的移动站Y坐标(东距离) */
    @Excel(name = "基站坐标下的移动站Y坐标(东距离)")
    private String eastDistance;

    /** 定位指示状态 */
    @Excel(name = "定位指示状态")
    private String positionIndicator;

    /** 定向指示状态 */
    @Excel(name = "定向指示状态")
    private String headingIndicator;

    /** 主导天线收星数 */
    @Excel(name = "主导天线收星数")
    private String svn;

    /** 从站参与解算的卫星数 */
    @Excel(name = "从站参与解算的卫星数")
    private String solutionSv;

    /** 差分延迟 */
    @Excel(name = "差分延迟")
    private String diffAge;

    /** 设备序列号 */
    @Excel(name = "设备序列号")
    private String serialNo;

    /** 基准站ID */
    @Excel(name = "基准站ID")
    private String stationId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDeviceId(Integer deviceId) 
    {
        this.deviceId = deviceId;
    }

    public Integer getDeviceId() 
    {
        return deviceId;
    }

    public void setDeviceCode(String deviceCode) 
    {
        this.deviceCode = deviceCode;
    }

    public String getDeviceCode() 
    {
        return deviceCode;
    }

    public void setDeviceName(String deviceName) 
    {
        this.deviceName = deviceName;
    }

    public String getDeviceName() 
    {
        return deviceName;
    }

    public void setInitialTime(Long initialTime) 
    {
        this.initialTime = initialTime;
    }

    public Long getInitialTime() 
    {
        return initialTime;
    }

    public void setInitialBjTime(Date initialBjTime) 
    {
        this.initialBjTime = initialBjTime;
    }

    public Date getInitialBjTime() 
    {
        return initialBjTime;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    public void setUtc(String utc) 
    {
        this.utc = utc;
    }

    public String getUtc() 
    {
        return utc;
    }

    public void setLat(String lat) 
    {
        this.lat = lat;
    }

    public String getLat() 
    {
        return lat;
    }

    public void setLon(String lon) 
    {
        this.lon = lon;
    }

    public String getLon() 
    {
        return lon;
    }

    public void setHeading(String heading) 
    {
        this.heading = heading;
    }

    public String getHeading() 
    {
        return heading;
    }

    public void setRolling(String rolling) 
    {
        this.rolling = rolling;
    }

    public String getRolling() 
    {
        return rolling;
    }

    public void setPitch(String pitch) 
    {
        this.pitch = pitch;
    }

    public String getPitch() 
    {
        return pitch;
    }

    public void setHeight(String height) 
    {
        this.height = height;
    }

    public String getHeight() 
    {
        return height;
    }

    public void setElpHeight(String elpHeight) 
    {
        this.elpHeight = elpHeight;
    }

    public String getElpHeight() 
    {
        return elpHeight;
    }

    public void setDistance(String distance) 
    {
        this.distance = distance;
    }

    public String getDistance() 
    {
        return distance;
    }

    public void setBaselineLength(String baselineLength) 
    {
        this.baselineLength = baselineLength;
    }

    public String getBaselineLength() 
    {
        return baselineLength;
    }

    public void setVelN(String velN) 
    {
        this.velN = velN;
    }

    public String getVelN() 
    {
        return velN;
    }

    public void setVelE(String velE) 
    {
        this.velE = velE;
    }

    public String getVelE() 
    {
        return velE;
    }

    public void setVelD(String velD) 
    {
        this.velD = velD;
    }

    public String getVelD() 
    {
        return velD;
    }

    public void setVelG(String velG) 
    {
        this.velG = velG;
    }

    public String getVelG() 
    {
        return velG;
    }

    public void setCoordinateNorthing(String coordinateNorthing) 
    {
        this.coordinateNorthing = coordinateNorthing;
    }

    public String getCoordinateNorthing() 
    {
        return coordinateNorthing;
    }

    public void setCoordinateEasting(String coordinateEasting) 
    {
        this.coordinateEasting = coordinateEasting;
    }

    public String getCoordinateEasting() 
    {
        return coordinateEasting;
    }

    public void setNorthDistance(String northDistance) 
    {
        this.northDistance = northDistance;
    }

    public String getNorthDistance() 
    {
        return northDistance;
    }

    public void setEastDistance(String eastDistance) 
    {
        this.eastDistance = eastDistance;
    }

    public String getEastDistance() 
    {
        return eastDistance;
    }

    public void setPositionIndicator(String positionIndicator) 
    {
        this.positionIndicator = positionIndicator;
    }

    public String getPositionIndicator() 
    {
        return positionIndicator;
    }

    public void setHeadingIndicator(String headingIndicator) 
    {
        this.headingIndicator = headingIndicator;
    }

    public String getHeadingIndicator() 
    {
        return headingIndicator;
    }

    public void setSvn(String svn) 
    {
        this.svn = svn;
    }

    public String getSvn() 
    {
        return svn;
    }

    public void setSolutionSv(String solutionSv) 
    {
        this.solutionSv = solutionSv;
    }

    public String getSolutionSv() 
    {
        return solutionSv;
    }

    public void setDiffAge(String diffAge) 
    {
        this.diffAge = diffAge;
    }

    public String getDiffAge() 
    {
        return diffAge;
    }

    public void setSerialNo(String serialNo) 
    {
        this.serialNo = serialNo;
    }

    public String getSerialNo() 
    {
        return serialNo;
    }

    public void setStationId(String stationId) 
    {
        this.stationId = stationId;
    }

    public String getStationId() 
    {
        return stationId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deviceId", getDeviceId())
            .append("deviceCode", getDeviceCode())
            .append("deviceName", getDeviceName())
            .append("initialTime", getInitialTime())
            .append("initialBjTime", getInitialBjTime())
            .append("status", getStatus())
            .append("utc", getUtc())
            .append("lat", getLat())
            .append("lon", getLon())
            .append("heading", getHeading())
            .append("rolling", getRolling())
            .append("pitch", getPitch())
            .append("height", getHeight())
            .append("elpHeight", getElpHeight())
            .append("distance", getDistance())
            .append("baselineLength", getBaselineLength())
            .append("velN", getVelN())
            .append("velE", getVelE())
            .append("velD", getVelD())
            .append("velG", getVelG())
            .append("coordinateNorthing", getCoordinateNorthing())
            .append("coordinateEasting", getCoordinateEasting())
            .append("northDistance", getNorthDistance())
            .append("eastDistance", getEastDistance())
            .append("positionIndicator", getPositionIndicator())
            .append("headingIndicator", getHeadingIndicator())
            .append("svn", getSvn())
            .append("solutionSv", getSolutionSv())
            .append("diffAge", getDiffAge())
            .append("serialNo", getSerialNo())
            .append("stationId", getStationId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
