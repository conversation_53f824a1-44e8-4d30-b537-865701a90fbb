package com.snct.system.domain.data;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;

/**
 * 气象站设备数据对象 bu_data_aws
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public class BuDataAws extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 设备ID
     */
    @Excel(name = "设备ID")
    private Integer deviceId;

    /**
     * 设备编号
     */
    @Excel(name = "设备编号")
    private String deviceCode;

    /**
     * 设备名称
     */
    @Excel(name = "设备名称")
    private String deviceName;

    /**
     * 数据采集时间戳
     */
    @Excel(name = "数据采集时间戳")
    private Long initialTime;

    /**
     * 北京时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "北京时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date initialBjTime;

    /**
     * 数据状态(0-正常,1-异常)
     */
    @Excel(name = "数据状态(0-正常,1-异常)")
    private Long status;

    /**
     * 相对风向(度)
     */
    @Excel(name = "相对风向(度)")
    private BigDecimal relativeWind;

    /**
     * 相对风速
     */
    @Excel(name = "相对风速")
    private BigDecimal relativeWindSpeed;

    /**
     * 真实风向(度)
     */
    @Excel(name = "真实风向(度)")
    private BigDecimal trueWind;

    /**
     * 真实风速
     */
    @Excel(name = "真实风速")
    private BigDecimal trueWindSpeed;

    /**
     * 风速单位(M/K/N)
     */
    @Excel(name = "风速单位(M/K/N)")
    private String windSpeedUnit;

    /**
     * 气温(℃)
     */
    @Excel(name = "气温(℃)")
    private BigDecimal airTemperature;

    /**
     * 相对湿度(%)
     */
    @Excel(name = "相对湿度(%)")
    private BigDecimal humidity;

    /**
     * 露点温度(℃)
     */
    @Excel(name = "露点温度(℃)")
    private BigDecimal dewPoint;

    /**
     * 大气压力(hPa)
     */
    @Excel(name = "大气压力(hPa)")
    private BigDecimal pressure;

    /**
     * QFE气压(hPa)
     */
    @Excel(name = "QFE气压(hPa)")
    private BigDecimal qfe;

    /**
     * QNH气压(hPa)
     */
    @Excel(name = "QNH气压(hPa)")
    private BigDecimal qnh;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setDeviceId(Integer deviceId) {
        this.deviceId = deviceId;
    }

    public Integer getDeviceId() {
        return deviceId;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setInitialTime(Long initialTime) {
        this.initialTime = initialTime;
    }

    public Long getInitialTime() {
        return initialTime;
    }

    public void setInitialBjTime(Date initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public Date getInitialBjTime() {
        return initialBjTime;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public Long getStatus() {
        return status;
    }

    public void setRelativeWind(BigDecimal relativeWind) {
        this.relativeWind = relativeWind;
    }

    public BigDecimal getRelativeWind() {
        return relativeWind;
    }

    public void setRelativeWindSpeed(BigDecimal relativeWindSpeed) {
        this.relativeWindSpeed = relativeWindSpeed;
    }

    public BigDecimal getRelativeWindSpeed() {
        return relativeWindSpeed;
    }

    public void setTrueWind(BigDecimal trueWind) {
        this.trueWind = trueWind;
    }

    public BigDecimal getTrueWind() {
        return trueWind;
    }

    public void setTrueWindSpeed(BigDecimal trueWindSpeed) {
        this.trueWindSpeed = trueWindSpeed;
    }

    public BigDecimal getTrueWindSpeed() {
        return trueWindSpeed;
    }

    public void setWindSpeedUnit(String windSpeedUnit) {
        this.windSpeedUnit = windSpeedUnit;
    }

    public String getWindSpeedUnit() {
        return windSpeedUnit;
    }

    public void setAirTemperature(BigDecimal airTemperature) {
        this.airTemperature = airTemperature;
    }

    public BigDecimal getAirTemperature() {
        return airTemperature;
    }

    public void setHumidity(BigDecimal humidity) {
        this.humidity = humidity;
    }

    public BigDecimal getHumidity() {
        return humidity;
    }

    public void setDewPoint(BigDecimal dewPoint) {
        this.dewPoint = dewPoint;
    }

    public BigDecimal getDewPoint() {
        return dewPoint;
    }

    public void setPressure(BigDecimal pressure) {
        this.pressure = pressure;
    }

    public BigDecimal getPressure() {
        return pressure;
    }

    public void setQfe(BigDecimal qfe) {
        this.qfe = qfe;
    }

    public BigDecimal getQfe() {
        return qfe;
    }

    public void setQnh(BigDecimal qnh) {
        this.qnh = qnh;
    }

    public BigDecimal getQnh() {
        return qnh;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("deviceId", getDeviceId())
                .append("deviceCode", getDeviceCode())
                .append("deviceName", getDeviceName())
                .append("initialTime", getInitialTime())
                .append("initialBjTime", getInitialBjTime())
                .append("status", getStatus())
                .append("relativeWind", getRelativeWind())
                .append("relativeWindSpeed", getRelativeWindSpeed())
                .append("trueWind", getTrueWind())
                .append("trueWindSpeed", getTrueWindSpeed())
                .append("windSpeedUnit", getWindSpeedUnit())
                .append("airTemperature", getAirTemperature())
                .append("humidity", getHumidity())
                .append("dewPoint", getDewPoint())
                .append("pressure", getPressure())
                .append("qfe", getQfe())
                .append("qnh", getQnh())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
