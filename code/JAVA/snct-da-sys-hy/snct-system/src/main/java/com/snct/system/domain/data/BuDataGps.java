package com.snct.system.domain.data;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;

/**
 * GPS/北斗设备数据对象 bu_data_gps
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public class BuDataGps extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 设备ID
     */
    @Excel(name = "设备ID")
    private Integer deviceId;

    /**
     * 设备编号
     */
    @Excel(name = "设备编号")
    private String deviceCode;

    /**
     * 设备名称
     */
    @Excel(name = "设备名称")
    private String deviceName;

    /**
     * 数据采集时间戳
     */
    @Excel(name = "数据采集时间戳")
    private Long initialTime;

    /**
     * 北京时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "北京时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date initialBjTime;

    /**
     * 数据状态(0-正常,1-异常)
     */
    @Excel(name = "数据状态(0-正常,1-异常)")
    private Integer status;

    /**
     * UTC时间
     */
    @Excel(name = "UTC时间")
    private String utcTime;

    /**
     * 纬度
     */
    @Excel(name = "纬度")
    private BigDecimal latitude;

    /**
     * 经度
     */
    @Excel(name = "经度")
    private BigDecimal longitude;

    /**
     * 纬度半球(N/S)
     */
    @Excel(name = "纬度半球(N/S)")
    private String latitudeHemisphere;

    /**
     * 经度半球(E/W)
     */
    @Excel(name = "经度半球(E/W)")
    private String longitudeHemisphere;

    /**
     * 定位质量指示
     */
    @Excel(name = "定位质量指示")
    private Integer positionQuality;

    /**
     * 使用卫星数量
     */
    @Excel(name = "使用卫星数量")
    private Integer satellitesCount;

    /**
     * 水平精确度
     */
    @Excel(name = "水平精确度")
    private BigDecimal horizontalAccuracy;

    /**
     * 天线高度(米)
     */
    @Excel(name = "天线高度(米)")
    private BigDecimal antennaHeight;

    /**
     * 速度
     */
    @Excel(name = "速度")
    private BigDecimal speed;

    /**
     * 航向
     */
    @Excel(name = "航向")
    private BigDecimal course;

    /**
     * GPS日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "GPS日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gpsDate;

    /**
     * 日
     */
    @Excel(name = "日")
    private Integer day;

    /**
     * 月
     */
    @Excel(name = "月")
    private Integer month;

    /**
     * 年
     */
    @Excel(name = "年")
    private Integer year;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setDeviceId(Integer deviceId) {
        this.deviceId = deviceId;
    }

    public Integer getDeviceId() {
        return deviceId;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setInitialTime(Long initialTime) {
        this.initialTime = initialTime;
    }

    public Long getInitialTime() {
        return initialTime;
    }

    public void setInitialBjTime(Date initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public Date getInitialBjTime() {
        return initialBjTime;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public void setUtcTime(String utcTime) {
        this.utcTime = utcTime;
    }

    public String getUtcTime() {
        return utcTime;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLatitudeHemisphere(String latitudeHemisphere) {
        this.latitudeHemisphere = latitudeHemisphere;
    }

    public String getLatitudeHemisphere() {
        return latitudeHemisphere;
    }

    public void setLongitudeHemisphere(String longitudeHemisphere) {
        this.longitudeHemisphere = longitudeHemisphere;
    }

    public String getLongitudeHemisphere() {
        return longitudeHemisphere;
    }

    public void setPositionQuality(Integer positionQuality) {
        this.positionQuality = positionQuality;
    }

    public Integer getPositionQuality() {
        return positionQuality;
    }

    public void setSatellitesCount(Integer satellitesCount) {
        this.satellitesCount = satellitesCount;
    }

    public Integer getSatellitesCount() {
        return satellitesCount;
    }

    public void setHorizontalAccuracy(BigDecimal horizontalAccuracy) {
        this.horizontalAccuracy = horizontalAccuracy;
    }

    public BigDecimal getHorizontalAccuracy() {
        return horizontalAccuracy;
    }

    public void setAntennaHeight(BigDecimal antennaHeight) {
        this.antennaHeight = antennaHeight;
    }

    public BigDecimal getAntennaHeight() {
        return antennaHeight;
    }

    public void setSpeed(BigDecimal speed) {
        this.speed = speed;
    }

    public BigDecimal getSpeed() {
        return speed;
    }

    public void setCourse(BigDecimal course) {
        this.course = course;
    }

    public BigDecimal getCourse() {
        return course;
    }

    public void setGpsDate(Date gpsDate) {
        this.gpsDate = gpsDate;
    }

    public Date getGpsDate() {
        return gpsDate;
    }

    public void setDay(Integer day) {
        this.day = day;
    }

    public Integer getDay() {
        return day;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public Integer getMonth() {
        return month;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getYear() {
        return year;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("deviceId", getDeviceId())
                .append("deviceCode", getDeviceCode())
                .append("deviceName", getDeviceName())
                .append("initialTime", getInitialTime())
                .append("initialBjTime", getInitialBjTime())
                .append("status", getStatus())
                .append("utcTime", getUtcTime())
                .append("latitude", getLatitude())
                .append("longitude", getLongitude())
                .append("latitudeHemisphere", getLatitudeHemisphere())
                .append("longitudeHemisphere", getLongitudeHemisphere())
                .append("positionQuality", getPositionQuality())
                .append("satellitesCount", getSatellitesCount())
                .append("horizontalAccuracy", getHorizontalAccuracy())
                .append("antennaHeight", getAntennaHeight())
                .append("speed", getSpeed())
                .append("course", getCourse())
                .append("gpsDate", getGpsDate())
                .append("day", getDay())
                .append("month", getMonth())
                .append("year", getYear())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
