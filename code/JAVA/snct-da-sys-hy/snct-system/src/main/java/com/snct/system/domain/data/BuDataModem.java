package com.snct.system.domain.data;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;

/**
 * Modem设备数据对象 bu_data_modem
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public class BuDataModem extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 设备ID
     */
    @Excel(name = "设备ID")
    private Integer deviceId;

    /**
     * 设备编号
     */
    @Excel(name = "设备编号")
    private String deviceCode;

    /**
     * 设备名称
     */
    @Excel(name = "设备名称")
    private String deviceName;

    /**
     * 数据采集时间戳
     */
    @Excel(name = "数据采集时间戳")
    private Long initialTime;

    /**
     * 北京时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "北京时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date initialBjTime;

    /**
     * 数据状态(0-正常,1-异常)
     */
    @Excel(name = "数据状态(0-正常,1-异常)")
    private Integer status;

    /**
     * 信号强度
     */
    @Excel(name = "信号强度")
    private BigDecimal signal;

    /**
     * 速率
     */
    @Excel(name = "速率")
    private BigDecimal speed;

    /**
     * 发送功率
     */
    @Excel(name = "发送功率")
    private BigDecimal sendPower;

    /**
     * 状态标志
     */
    @Excel(name = "状态标志")
    private Integer isFlag;

    /**
     * 设备状态(0-默认,1-发送云端成功,2-发送云端失败)
     */
    @Excel(name = "设备状态(0-默认,1-发送云端成功,2-发送云端失败)")
    private Integer deviceStatus;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setDeviceId(Integer deviceId) {
        this.deviceId = deviceId;
    }

    public Integer getDeviceId() {
        return deviceId;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setInitialTime(Long initialTime) {
        this.initialTime = initialTime;
    }

    public Long getInitialTime() {
        return initialTime;
    }

    public void setInitialBjTime(Date initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public Date getInitialBjTime() {
        return initialBjTime;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public void setSignal(BigDecimal signal) {
        this.signal = signal;
    }

    public BigDecimal getSignal() {
        return signal;
    }

    public void setSpeed(BigDecimal speed) {
        this.speed = speed;
    }

    public BigDecimal getSpeed() {
        return speed;
    }

    public void setSendPower(BigDecimal sendPower) {
        this.sendPower = sendPower;
    }

    public BigDecimal getSendPower() {
        return sendPower;
    }

    public void setIsFlag(Integer isFlag) {
        this.isFlag = isFlag;
    }

    public Integer getIsFlag() {
        return isFlag;
    }

    public void setDeviceStatus(Integer deviceStatus) {
        this.deviceStatus = deviceStatus;
    }

    public Integer getDeviceStatus() {
        return deviceStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("deviceId", getDeviceId())
                .append("deviceCode", getDeviceCode())
                .append("deviceName", getDeviceName())
                .append("initialTime", getInitialTime())
                .append("initialBjTime", getInitialBjTime())
                .append("status", getStatus())
                .append("signal", getSignal())
                .append("speed", getSpeed())
                .append("sendPower", getSendPower())
                .append("isFlag", getIsFlag())
                .append("deviceStatus", getDeviceStatus())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
