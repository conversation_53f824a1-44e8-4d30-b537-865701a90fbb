package com.snct.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;

/**
 * PDU设备数据对象 bu_data_pdu
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
public class BuDataPdu extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 设备ID */
    @Excel(name = "设备ID")
    private Integer deviceId;

    /** 设备编号 */
    @Excel(name = "设备编号")
    private String deviceCode;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String deviceName;

    /** 数据采集时间戳 */
    @Excel(name = "数据采集时间戳")
    private Long initialTime;

    /** 北京时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "北京时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date initialBjTime;

    /** 数据状态(0-正常,1-异常) */
    @Excel(name = "数据状态(0-正常,1-异常)")
    private Long status;

    /** 插座1电流(A) */
    @Excel(name = "插座1电流(A)")
    private BigDecimal electric1;

    /** 插座1功率(W) */
    @Excel(name = "插座1功率(W)")
    private BigDecimal power1;

    /** 插座1状态 */
    @Excel(name = "插座1状态")
    private Long status1;

    /** 插座2电流(A) */
    @Excel(name = "插座2电流(A)")
    private BigDecimal electric2;

    /** 插座2功率(W) */
    @Excel(name = "插座2功率(W)")
    private BigDecimal power2;

    /** 插座2状态 */
    @Excel(name = "插座2状态")
    private Long status2;

    /** 插座3电流(A) */
    @Excel(name = "插座3电流(A)")
    private BigDecimal electric3;

    /** 插座3功率(W) */
    @Excel(name = "插座3功率(W)")
    private BigDecimal power3;

    /** 插座3状态 */
    @Excel(name = "插座3状态")
    private Long status3;

    /** 插座4电流(A) */
    @Excel(name = "插座4电流(A)")
    private BigDecimal electric4;

    /** 插座4功率(W) */
    @Excel(name = "插座4功率(W)")
    private BigDecimal power4;

    /** 插座4状态 */
    @Excel(name = "插座4状态")
    private Long status4;

    /** 插座5电流(A) */
    @Excel(name = "插座5电流(A)")
    private BigDecimal electric5;

    /** 插座5功率(W) */
    @Excel(name = "插座5功率(W)")
    private BigDecimal power5;

    /** 插座5状态 */
    @Excel(name = "插座5状态")
    private Long status5;

    /** 插座6电流(A) */
    @Excel(name = "插座6电流(A)")
    private BigDecimal electric6;

    /** 插座6功率(W) */
    @Excel(name = "插座6功率(W)")
    private BigDecimal power6;

    /** 插座6状态 */
    @Excel(name = "插座6状态")
    private Long status6;

    /** 插座7电流(A) */
    @Excel(name = "插座7电流(A)")
    private BigDecimal electric7;

    /** 插座7功率(W) */
    @Excel(name = "插座7功率(W)")
    private BigDecimal power7;

    /** 插座7状态 */
    @Excel(name = "插座7状态")
    private Long status7;

    /** 插座8电流(A) */
    @Excel(name = "插座8电流(A)")
    private BigDecimal electric8;

    /** 插座8功率(W) */
    @Excel(name = "插座8功率(W)")
    private BigDecimal power8;

    /** 插座8状态 */
    @Excel(name = "插座8状态")
    private Long status8;

    /** 总电流(A) */
    @Excel(name = "总电流(A)")
    private BigDecimal electric;

    /** 管理电能(kWh) */
    @Excel(name = "管理电能(kWh)")
    private BigDecimal manage;

    /** 电压(V) */
    @Excel(name = "电压(V)")
    private BigDecimal voltage;

    /** 有功功率(kW) */
    @Excel(name = "有功功率(kW)")
    private BigDecimal yesPower;

    /** 无功功率(kVar) */
    @Excel(name = "无功功率(kVar)")
    private BigDecimal noPower;

    /** 视在功率(kVA) */
    @Excel(name = "视在功率(kVA)")
    private BigDecimal seePower;

    /** 功率参数 */
    @Excel(name = "功率参数")
    private Long powerParam;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDeviceId(Integer deviceId) 
    {
        this.deviceId = deviceId;
    }

    public Integer getDeviceId() 
    {
        return deviceId;
    }

    public void setDeviceCode(String deviceCode) 
    {
        this.deviceCode = deviceCode;
    }

    public String getDeviceCode() 
    {
        return deviceCode;
    }

    public void setDeviceName(String deviceName) 
    {
        this.deviceName = deviceName;
    }

    public String getDeviceName() 
    {
        return deviceName;
    }

    public void setInitialTime(Long initialTime) 
    {
        this.initialTime = initialTime;
    }

    public Long getInitialTime() 
    {
        return initialTime;
    }

    public void setInitialBjTime(Date initialBjTime) 
    {
        this.initialBjTime = initialBjTime;
    }

    public Date getInitialBjTime() 
    {
        return initialBjTime;
    }

    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }

    public void setElectric1(BigDecimal electric1) 
    {
        this.electric1 = electric1;
    }

    public BigDecimal getElectric1() 
    {
        return electric1;
    }

    public void setPower1(BigDecimal power1) 
    {
        this.power1 = power1;
    }

    public BigDecimal getPower1() 
    {
        return power1;
    }

    public void setStatus1(Long status1) 
    {
        this.status1 = status1;
    }

    public Long getStatus1() 
    {
        return status1;
    }

    public void setElectric2(BigDecimal electric2) 
    {
        this.electric2 = electric2;
    }

    public BigDecimal getElectric2() 
    {
        return electric2;
    }

    public void setPower2(BigDecimal power2) 
    {
        this.power2 = power2;
    }

    public BigDecimal getPower2() 
    {
        return power2;
    }

    public void setStatus2(Long status2) 
    {
        this.status2 = status2;
    }

    public Long getStatus2() 
    {
        return status2;
    }

    public void setElectric3(BigDecimal electric3) 
    {
        this.electric3 = electric3;
    }

    public BigDecimal getElectric3() 
    {
        return electric3;
    }

    public void setPower3(BigDecimal power3) 
    {
        this.power3 = power3;
    }

    public BigDecimal getPower3() 
    {
        return power3;
    }

    public void setStatus3(Long status3) 
    {
        this.status3 = status3;
    }

    public Long getStatus3() 
    {
        return status3;
    }

    public void setElectric4(BigDecimal electric4) 
    {
        this.electric4 = electric4;
    }

    public BigDecimal getElectric4() 
    {
        return electric4;
    }

    public void setPower4(BigDecimal power4) 
    {
        this.power4 = power4;
    }

    public BigDecimal getPower4() 
    {
        return power4;
    }

    public void setStatus4(Long status4) 
    {
        this.status4 = status4;
    }

    public Long getStatus4() 
    {
        return status4;
    }

    public void setElectric5(BigDecimal electric5) 
    {
        this.electric5 = electric5;
    }

    public BigDecimal getElectric5() 
    {
        return electric5;
    }

    public void setPower5(BigDecimal power5) 
    {
        this.power5 = power5;
    }

    public BigDecimal getPower5() 
    {
        return power5;
    }

    public void setStatus5(Long status5) 
    {
        this.status5 = status5;
    }

    public Long getStatus5() 
    {
        return status5;
    }

    public void setElectric6(BigDecimal electric6) 
    {
        this.electric6 = electric6;
    }

    public BigDecimal getElectric6() 
    {
        return electric6;
    }

    public void setPower6(BigDecimal power6) 
    {
        this.power6 = power6;
    }

    public BigDecimal getPower6() 
    {
        return power6;
    }

    public void setStatus6(Long status6) 
    {
        this.status6 = status6;
    }

    public Long getStatus6() 
    {
        return status6;
    }

    public void setElectric7(BigDecimal electric7) 
    {
        this.electric7 = electric7;
    }

    public BigDecimal getElectric7() 
    {
        return electric7;
    }

    public void setPower7(BigDecimal power7) 
    {
        this.power7 = power7;
    }

    public BigDecimal getPower7() 
    {
        return power7;
    }

    public void setStatus7(Long status7) 
    {
        this.status7 = status7;
    }

    public Long getStatus7() 
    {
        return status7;
    }

    public void setElectric8(BigDecimal electric8) 
    {
        this.electric8 = electric8;
    }

    public BigDecimal getElectric8() 
    {
        return electric8;
    }

    public void setPower8(BigDecimal power8) 
    {
        this.power8 = power8;
    }

    public BigDecimal getPower8() 
    {
        return power8;
    }

    public void setStatus8(Long status8) 
    {
        this.status8 = status8;
    }

    public Long getStatus8() 
    {
        return status8;
    }

    public void setElectric(BigDecimal electric) 
    {
        this.electric = electric;
    }

    public BigDecimal getElectric() 
    {
        return electric;
    }

    public void setManage(BigDecimal manage) 
    {
        this.manage = manage;
    }

    public BigDecimal getManage() 
    {
        return manage;
    }

    public void setVoltage(BigDecimal voltage) 
    {
        this.voltage = voltage;
    }

    public BigDecimal getVoltage() 
    {
        return voltage;
    }

    public void setYesPower(BigDecimal yesPower) 
    {
        this.yesPower = yesPower;
    }

    public BigDecimal getYesPower() 
    {
        return yesPower;
    }

    public void setNoPower(BigDecimal noPower) 
    {
        this.noPower = noPower;
    }

    public BigDecimal getNoPower() 
    {
        return noPower;
    }

    public void setSeePower(BigDecimal seePower) 
    {
        this.seePower = seePower;
    }

    public BigDecimal getSeePower() 
    {
        return seePower;
    }

    public void setPowerParam(Long powerParam) 
    {
        this.powerParam = powerParam;
    }

    public Long getPowerParam() 
    {
        return powerParam;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deviceId", getDeviceId())
            .append("deviceCode", getDeviceCode())
            .append("deviceName", getDeviceName())
            .append("initialTime", getInitialTime())
            .append("initialBjTime", getInitialBjTime())
            .append("status", getStatus())
            .append("electric1", getElectric1())
            .append("power1", getPower1())
            .append("status1", getStatus1())
            .append("electric2", getElectric2())
            .append("power2", getPower2())
            .append("status2", getStatus2())
            .append("electric3", getElectric3())
            .append("power3", getPower3())
            .append("status3", getStatus3())
            .append("electric4", getElectric4())
            .append("power4", getPower4())
            .append("status4", getStatus4())
            .append("electric5", getElectric5())
            .append("power5", getPower5())
            .append("status5", getStatus5())
            .append("electric6", getElectric6())
            .append("power6", getPower6())
            .append("status6", getStatus6())
            .append("electric7", getElectric7())
            .append("power7", getPower7())
            .append("status7", getStatus7())
            .append("electric8", getElectric8())
            .append("power8", getPower8())
            .append("status8", getStatus8())
            .append("electric", getElectric())
            .append("manage", getManage())
            .append("voltage", getVoltage())
            .append("yesPower", getYesPower())
            .append("noPower", getNoPower())
            .append("seePower", getSeePower())
            .append("powerParam", getPowerParam())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
