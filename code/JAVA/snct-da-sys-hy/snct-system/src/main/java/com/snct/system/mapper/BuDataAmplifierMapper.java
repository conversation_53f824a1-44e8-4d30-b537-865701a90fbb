package com.snct.system.mapper;

import java.util.List;

import com.snct.system.domain.data.BuDataAmplifier;

/**
 * 功放设备数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface BuDataAmplifierMapper {
    /**
     * 查询功放设备数据
     *
     * @param id 功放设备数据主键
     * @return 功放设备数据
     */
    public BuDataAmplifier selectBuDataAmplifierById(Long id);

    /**
     * 查询功放设备数据列表
     *
     * @param buDataAmplifier 功放设备数据
     * @return 功放设备数据集合
     */
    public List<BuDataAmplifier> selectBuDataAmplifierList(BuDataAmplifier buDataAmplifier);

    /**
     * 新增功放设备数据
     *
     * @param buDataAmplifier 功放设备数据
     * @return 结果
     */
    public int insertBuDataAmplifier(BuDataAmplifier buDataAmplifier);

    /**
     * 修改功放设备数据
     *
     * @param buDataAmplifier 功放设备数据
     * @return 结果
     */
    public int updateBuDataAmplifier(BuDataAmplifier buDataAmplifier);

    /**
     * 删除功放设备数据
     *
     * @param id 功放设备数据主键
     * @return 结果
     */
    public int deleteBuDataAmplifierById(Long id);

    /**
     * 批量删除功放设备数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBuDataAmplifierByIds(Long[] ids);

    /**
     * 根据时间删除功放设备数据
     *
     * @param cutoffDate 截止日期，删除此日期之前的数据
     * @return 删除的记录数
     */
    public int deleteBuDataAmplifierByDate(String cutoffDate);
}
