package com.snct.system.mapper;

import java.util.List;
import com.snct.system.domain.data.BuDataAttitude;

/**
 * 姿态设备数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface BuDataAttitudeMapper 
{
    /**
     * 查询姿态设备数据
     * 
     * @param id 姿态设备数据主键
     * @return 姿态设备数据
     */
    public BuDataAttitude selectBuDataAttitudeById(Long id);

    /**
     * 查询姿态设备数据列表
     * 
     * @param buDataAttitude 姿态设备数据
     * @return 姿态设备数据集合
     */
    public List<BuDataAttitude> selectBuDataAttitudeList(BuDataAttitude buDataAttitude);

    /**
     * 新增姿态设备数据
     * 
     * @param buDataAttitude 姿态设备数据
     * @return 结果
     */
    public int insertBuDataAttitude(BuDataAttitude buDataAttitude);

    /**
     * 修改姿态设备数据
     * 
     * @param buDataAttitude 姿态设备数据
     * @return 结果
     */
    public int updateBuDataAttitude(BuDataAttitude buDataAttitude);

    /**
     * 删除姿态设备数据
     * 
     * @param id 姿态设备数据主键
     * @return 结果
     */
    public int deleteBuDataAttitudeById(Long id);

    /**
     * 批量删除姿态设备数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBuDataAttitudeByIds(Long[] ids);

    /**
     * 根据时间删除姿态设备数据
     *
     * @param cutoffDate 截止日期，删除此日期之前的数据
     * @return 删除的记录数
     */
    public int deleteBuDataAttitudeByDate(String cutoffDate);
}
