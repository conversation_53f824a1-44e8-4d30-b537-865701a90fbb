package com.snct.system.mapper;

import java.util.List;

import com.snct.system.domain.data.BuDataModem;

/**
 * Modem设备数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface BuDataModemMapper {
    /**
     * 查询Modem设备数据
     *
     * @param id Modem设备数据主键
     * @return Modem设备数据
     */
    public BuDataModem selectBuDataModemById(Long id);

    /**
     * 查询Modem设备数据列表
     *
     * @param buDataModem Modem设备数据
     * @return Modem设备数据集合
     */
    public List<BuDataModem> selectBuDataModemList(BuDataModem buDataModem);

    /**
     * 新增Modem设备数据
     *
     * @param buDataModem Modem设备数据
     * @return 结果
     */
    public int insertBuDataModem(BuDataModem buDataModem);

    /**
     * 修改Modem设备数据
     *
     * @param buDataModem Modem设备数据
     * @return 结果
     */
    public int updateBuDataModem(BuDataModem buDataModem);

    /**
     * 删除Modem设备数据
     *
     * @param id Modem设备数据主键
     * @return 结果
     */
    public int deleteBuDataModemById(Long id);

    /**
     * 批量删除Modem设备数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBuDataModemByIds(Long[] ids);
}
