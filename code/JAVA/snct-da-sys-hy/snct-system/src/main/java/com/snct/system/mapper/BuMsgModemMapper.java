package com.snct.system.mapper;

import com.snct.system.domain.msg.BuMsgModem;

import java.util.List;

/**
 * Modem消息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
public interface BuMsgModemMapper 
{
    /**
     * 查询Modem消息
     * 
     * @param id Modem消息主键
     * @return Modem消息
     */
    public BuMsgModem selectBuMsgModemById(Long id);

    /**
     * 查询Modem消息列表
     * 
     * @param buMsgModem Modem消息
     * @return Modem消息集合
     */
    public List<BuMsgModem> selectBuMsgModemList(BuMsgModem buMsgModem);

    /**
     * 新增Modem消息
     * 
     * @param buMsgModem Modem消息
     * @return 结果
     */
    public int insertBuMsgModem(BuMsgModem buMsgModem);

    /**
     * 修改Modem消息
     * 
     * @param buMsgModem Modem消息
     * @return 结果
     */
    public int updateBuMsgModem(BuMsgModem buMsgModem);

    /**
     * 删除Modem消息
     * 
     * @param id Modem消息主键
     * @return 结果
     */
    public int deleteBuMsgModemById(Long id);

    /**
     * 批量删除Modem消息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBuMsgModemByIds(Long[] ids);
}
