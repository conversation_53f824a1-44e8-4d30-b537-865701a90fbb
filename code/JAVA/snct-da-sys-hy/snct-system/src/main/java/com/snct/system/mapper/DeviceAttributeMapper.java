package com.snct.system.mapper;

import java.util.List;
import com.snct.system.domain.DeviceAttribute;

/**
 * 设备属性Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface DeviceAttributeMapper 
{
    /**
     * 查询设备属性
     * 
     * @param id 设备属性主键
     * @return 设备属性
     */
    public DeviceAttribute selectDeviceAttributeById(Long id);

    /**
     * 查询设备属性列表
     * 
     * @param deviceAttribute 设备属性
     * @return 设备属性集合
     */
    public List<DeviceAttribute> selectDeviceAttributeList(DeviceAttribute deviceAttribute);

    /**
     * 新增设备属性
     * 
     * @param deviceAttribute 设备属性
     * @return 结果
     */
    public int insertDeviceAttribute(DeviceAttribute deviceAttribute);

    /**
     * 修改设备属性
     * 
     * @param deviceAttribute 设备属性
     * @return 结果
     */
    public int updateDeviceAttribute(DeviceAttribute deviceAttribute);

    /**
     * 删除设备属性
     * 
     * @param id 设备属性主键
     * @return 结果
     */
    public int deleteDeviceAttributeById(Long id);

    /**
     * 批量删除设备属性
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDeviceAttributeByIds(Long[] ids);
}
