package com.snct.system.service;

import java.util.List;
import com.snct.system.domain.BuDataPdu;

/**
 * PDU设备数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
public interface IBuDataPduService 
{
    /**
     * 查询PDU设备数据
     * 
     * @param id PDU设备数据主键
     * @return PDU设备数据
     */
    public BuDataPdu selectBuDataPduById(Long id);

    /**
     * 查询PDU设备数据列表
     * 
     * @param buDataPdu PDU设备数据
     * @return PDU设备数据集合
     */
    public List<BuDataPdu> selectBuDataPduList(BuDataPdu buDataPdu);

    /**
     * 新增PDU设备数据
     * 
     * @param buDataPdu PDU设备数据
     * @return 结果
     */
    public int insertBuDataPdu(BuDataPdu buDataPdu);

    /**
     * 修改PDU设备数据
     * 
     * @param buDataPdu PDU设备数据
     * @return 结果
     */
    public int updateBuDataPdu(BuDataPdu buDataPdu);

    /**
     * 批量删除PDU设备数据
     * 
     * @param ids 需要删除的PDU设备数据主键集合
     * @return 结果
     */
    public int deleteBuDataPduByIds(Long[] ids);

    /**
     * 删除PDU设备数据信息
     * 
     * @param id PDU设备数据主键
     * @return 结果
     */
    public int deleteBuDataPduById(Long id);
}
