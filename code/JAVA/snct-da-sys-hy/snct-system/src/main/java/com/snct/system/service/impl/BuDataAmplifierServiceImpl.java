package com.snct.system.service.impl;

import java.util.List;

import com.snct.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.snct.system.mapper.BuDataAmplifierMapper;
import com.snct.system.domain.data.BuDataAmplifier;
import com.snct.system.service.IBuDataAmplifierService;

/**
 * 功放设备数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class BuDataAmplifierServiceImpl implements IBuDataAmplifierService {
    @Autowired
    private BuDataAmplifierMapper buDataAmplifierMapper;

    /**
     * 查询功放设备数据
     *
     * @param id 功放设备数据主键
     * @return 功放设备数据
     */
    @Override
    public BuDataAmplifier selectBuDataAmplifierById(Long id) {
        return buDataAmplifierMapper.selectBuDataAmplifierById(id);
    }

    /**
     * 查询功放设备数据列表
     *
     * @param buDataAmplifier 功放设备数据
     * @return 功放设备数据
     */
    @Override
    public List<BuDataAmplifier> selectBuDataAmplifierList(BuDataAmplifier buDataAmplifier) {
        return buDataAmplifierMapper.selectBuDataAmplifierList(buDataAmplifier);
    }

    /**
     * 新增功放设备数据
     *
     * @param buDataAmplifier 功放设备数据
     * @return 结果
     */
    @Override
    public int insertBuDataAmplifier(BuDataAmplifier buDataAmplifier) {
        buDataAmplifier.setCreateTime(DateUtils.getNowDate());
        return buDataAmplifierMapper.insertBuDataAmplifier(buDataAmplifier);
    }

    /**
     * 修改功放设备数据
     *
     * @param buDataAmplifier 功放设备数据
     * @return 结果
     */
    @Override
    public int updateBuDataAmplifier(BuDataAmplifier buDataAmplifier) {
        buDataAmplifier.setUpdateTime(DateUtils.getNowDate());
        return buDataAmplifierMapper.updateBuDataAmplifier(buDataAmplifier);
    }

    /**
     * 批量删除功放设备数据
     *
     * @param ids 需要删除的功放设备数据主键
     * @return 结果
     */
    @Override
    public int deleteBuDataAmplifierByIds(Long[] ids) {
        return buDataAmplifierMapper.deleteBuDataAmplifierByIds(ids);
    }

    /**
     * 删除功放设备数据信息
     *
     * @param id 功放设备数据主键
     * @return 结果
     */
    @Override
    public int deleteBuDataAmplifierById(Long id) {
        return buDataAmplifierMapper.deleteBuDataAmplifierById(id);
    }
}
