package com.snct.system.service.impl;

import java.util.List;
import com.snct.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.snct.system.mapper.BuDataAttitudeMapper;
import com.snct.system.domain.data.BuDataAttitude;
import com.snct.system.service.IBuDataAttitudeService;

/**
 * 姿态设备数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class BuDataAttitudeServiceImpl implements IBuDataAttitudeService 
{
    @Autowired
    private BuDataAttitudeMapper buDataAttitudeMapper;

    /**
     * 查询姿态设备数据
     * 
     * @param id 姿态设备数据主键
     * @return 姿态设备数据
     */
    @Override
    public BuDataAttitude selectBuDataAttitudeById(Long id)
    {
        return buDataAttitudeMapper.selectBuDataAttitudeById(id);
    }

    /**
     * 查询姿态设备数据列表
     * 
     * @param buDataAttitude 姿态设备数据
     * @return 姿态设备数据
     */
    @Override
    public List<BuDataAttitude> selectBuDataAttitudeList(BuDataAttitude buDataAttitude)
    {
        return buDataAttitudeMapper.selectBuDataAttitudeList(buDataAttitude);
    }

    /**
     * 新增姿态设备数据
     * 
     * @param buDataAttitude 姿态设备数据
     * @return 结果
     */
    @Override
    public int insertBuDataAttitude(BuDataAttitude buDataAttitude)
    {
        buDataAttitude.setCreateTime(DateUtils.getNowDate());
        return buDataAttitudeMapper.insertBuDataAttitude(buDataAttitude);
    }

    /**
     * 修改姿态设备数据
     * 
     * @param buDataAttitude 姿态设备数据
     * @return 结果
     */
    @Override
    public int updateBuDataAttitude(BuDataAttitude buDataAttitude)
    {
        buDataAttitude.setUpdateTime(DateUtils.getNowDate());
        return buDataAttitudeMapper.updateBuDataAttitude(buDataAttitude);
    }

    /**
     * 批量删除姿态设备数据
     * 
     * @param ids 需要删除的姿态设备数据主键
     * @return 结果
     */
    @Override
    public int deleteBuDataAttitudeByIds(Long[] ids)
    {
        return buDataAttitudeMapper.deleteBuDataAttitudeByIds(ids);
    }

    /**
     * 删除姿态设备数据信息
     * 
     * @param id 姿态设备数据主键
     * @return 结果
     */
    @Override
    public int deleteBuDataAttitudeById(Long id)
    {
        return buDataAttitudeMapper.deleteBuDataAttitudeById(id);
    }
}
