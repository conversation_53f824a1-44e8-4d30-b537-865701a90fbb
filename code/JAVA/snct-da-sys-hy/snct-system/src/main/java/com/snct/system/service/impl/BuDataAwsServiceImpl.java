package com.snct.system.service.impl;

import java.util.List;

import com.snct.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.snct.system.mapper.BuDataAwsMapper;
import com.snct.system.domain.data.BuDataAws;
import com.snct.system.service.IBuDataAwsService;

/**
 * 气象站设备数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class BuDataAwsServiceImpl implements IBuDataAwsService {
    @Autowired
    private BuDataAwsMapper buDataAwsMapper;

    /**
     * 查询气象站设备数据
     *
     * @param id 气象站设备数据主键
     * @return 气象站设备数据
     */
    @Override
    public BuDataAws selectBuDataAwsById(Long id) {
        return buDataAwsMapper.selectBuDataAwsById(id);
    }

    /**
     * 查询气象站设备数据列表
     *
     * @param buDataAws 气象站设备数据
     * @return 气象站设备数据
     */
    @Override
    public List<BuDataAws> selectBuDataAwsList(BuDataAws buDataAws) {
        return buDataAwsMapper.selectBuDataAwsList(buDataAws);
    }

    /**
     * 新增气象站设备数据
     *
     * @param buDataAws 气象站设备数据
     * @return 结果
     */
    @Override
    public int insertBuDataAws(BuDataAws buDataAws) {
        buDataAws.setCreateTime(DateUtils.getNowDate());
        return buDataAwsMapper.insertBuDataAws(buDataAws);
    }

    /**
     * 修改气象站设备数据
     *
     * @param buDataAws 气象站设备数据
     * @return 结果
     */
    @Override
    public int updateBuDataAws(BuDataAws buDataAws) {
        buDataAws.setUpdateTime(DateUtils.getNowDate());
        return buDataAwsMapper.updateBuDataAws(buDataAws);
    }

    /**
     * 批量删除气象站设备数据
     *
     * @param ids 需要删除的气象站设备数据主键
     * @return 结果
     */
    @Override
    public int deleteBuDataAwsByIds(Long[] ids) {
        return buDataAwsMapper.deleteBuDataAwsByIds(ids);
    }

    /**
     * 删除气象站设备数据信息
     *
     * @param id 气象站设备数据主键
     * @return 结果
     */
    @Override
    public int deleteBuDataAwsById(Long id) {
        return buDataAwsMapper.deleteBuDataAwsById(id);
    }
}
