package com.snct.system.service.impl;

import java.util.List;

import com.snct.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.snct.system.mapper.BuDataGpsMapper;
import com.snct.system.domain.data.BuDataGps;
import com.snct.system.service.IBuDataGpsService;

/**
 * GPS/北斗设备数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class BuDataGpsServiceImpl implements IBuDataGpsService {
    @Autowired
    private BuDataGpsMapper buDataGpsMapper;

    /**
     * 查询GPS/北斗设备数据
     *
     * @param id GPS/北斗设备数据主键
     * @return GPS/北斗设备数据
     */
    @Override
    public BuDataGps selectBuDataGpsById(Long id) {
        return buDataGpsMapper.selectBuDataGpsById(id);
    }

    /**
     * 查询GPS/北斗设备数据列表
     *
     * @param buDataGps GPS/北斗设备数据
     * @return GPS/北斗设备数据
     */
    @Override
    public List<BuDataGps> selectBuDataGpsList(BuDataGps buDataGps) {
        return buDataGpsMapper.selectBuDataGpsList(buDataGps);
    }

    /**
     * 新增GPS/北斗设备数据
     *
     * @param buDataGps GPS/北斗设备数据
     * @return 结果
     */
    @Override
    public int insertBuDataGps(BuDataGps buDataGps) {
        buDataGps.setCreateTime(DateUtils.getNowDate());
        return buDataGpsMapper.insertBuDataGps(buDataGps);
    }

    /**
     * 修改GPS/北斗设备数据
     *
     * @param buDataGps GPS/北斗设备数据
     * @return 结果
     */
    @Override
    public int updateBuDataGps(BuDataGps buDataGps) {
        buDataGps.setUpdateTime(DateUtils.getNowDate());
        return buDataGpsMapper.updateBuDataGps(buDataGps);
    }

    /**
     * 批量删除GPS/北斗设备数据
     *
     * @param ids 需要删除的GPS/北斗设备数据主键
     * @return 结果
     */
    @Override
    public int deleteBuDataGpsByIds(Long[] ids) {
        return buDataGpsMapper.deleteBuDataGpsByIds(ids);
    }

    /**
     * 删除GPS/北斗设备数据信息
     *
     * @param id GPS/北斗设备数据主键
     * @return 结果
     */
    @Override
    public int deleteBuDataGpsById(Long id) {
        return buDataGpsMapper.deleteBuDataGpsById(id);
    }
}
