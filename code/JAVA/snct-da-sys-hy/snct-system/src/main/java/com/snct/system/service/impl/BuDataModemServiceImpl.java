package com.snct.system.service.impl;

import java.util.List;

import com.snct.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.snct.system.mapper.BuDataModemMapper;
import com.snct.system.domain.data.BuDataModem;
import com.snct.system.service.IBuDataModemService;

/**
 * Modem设备数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class BuDataModemServiceImpl implements IBuDataModemService {
    @Autowired
    private BuDataModemMapper buDataModemMapper;

    /**
     * 查询Modem设备数据
     *
     * @param id Modem设备数据主键
     * @return Modem设备数据
     */
    @Override
    public BuDataModem selectBuDataModemById(Long id) {
        return buDataModemMapper.selectBuDataModemById(id);
    }

    /**
     * 查询Modem设备数据列表
     *
     * @param buDataModem Modem设备数据
     * @return Modem设备数据
     */
    @Override
    public List<BuDataModem> selectBuDataModemList(BuDataModem buDataModem) {
        return buDataModemMapper.selectBuDataModemList(buDataModem);
    }

    /**
     * 新增Modem设备数据
     *
     * @param buDataModem Modem设备数据
     * @return 结果
     */
    @Override
    public int insertBuDataModem(BuDataModem buDataModem) {
        buDataModem.setCreateTime(DateUtils.getNowDate());
        return buDataModemMapper.insertBuDataModem(buDataModem);
    }

    /**
     * 修改Modem设备数据
     *
     * @param buDataModem Modem设备数据
     * @return 结果
     */
    @Override
    public int updateBuDataModem(BuDataModem buDataModem) {
        buDataModem.setUpdateTime(DateUtils.getNowDate());
        return buDataModemMapper.updateBuDataModem(buDataModem);
    }

    /**
     * 批量删除Modem设备数据
     *
     * @param ids 需要删除的Modem设备数据主键
     * @return 结果
     */
    @Override
    public int deleteBuDataModemByIds(Long[] ids) {
        return buDataModemMapper.deleteBuDataModemByIds(ids);
    }

    /**
     * 删除Modem设备数据信息
     *
     * @param id Modem设备数据主键
     * @return 结果
     */
    @Override
    public int deleteBuDataModemById(Long id) {
        return buDataModemMapper.deleteBuDataModemById(id);
    }
}
