package com.snct.system.service.impl;

import java.util.List;
import com.snct.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.snct.system.mapper.BuDataPduMapper;
import com.snct.system.domain.BuDataPdu;
import com.snct.system.service.IBuDataPduService;

/**
 * PDU设备数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@Service
public class BuDataPduServiceImpl implements IBuDataPduService 
{
    @Autowired
    private BuDataPduMapper buDataPduMapper;

    /**
     * 查询PDU设备数据
     * 
     * @param id PDU设备数据主键
     * @return PDU设备数据
     */
    @Override
    public BuDataPdu selectBuDataPduById(Long id)
    {
        return buDataPduMapper.selectBuDataPduById(id);
    }

    /**
     * 查询PDU设备数据列表
     * 
     * @param buDataPdu PDU设备数据
     * @return PDU设备数据
     */
    @Override
    public List<BuDataPdu> selectBuDataPduList(BuDataPdu buDataPdu)
    {
        return buDataPduMapper.selectBuDataPduList(buDataPdu);
    }

    /**
     * 新增PDU设备数据
     * 
     * @param buDataPdu PDU设备数据
     * @return 结果
     */
    @Override
    public int insertBuDataPdu(BuDataPdu buDataPdu)
    {
        buDataPdu.setCreateTime(DateUtils.getNowDate());
        return buDataPduMapper.insertBuDataPdu(buDataPdu);
    }

    /**
     * 修改PDU设备数据
     * 
     * @param buDataPdu PDU设备数据
     * @return 结果
     */
    @Override
    public int updateBuDataPdu(BuDataPdu buDataPdu)
    {
        buDataPdu.setUpdateTime(DateUtils.getNowDate());
        return buDataPduMapper.updateBuDataPdu(buDataPdu);
    }

    /**
     * 批量删除PDU设备数据
     * 
     * @param ids 需要删除的PDU设备数据主键
     * @return 结果
     */
    @Override
    public int deleteBuDataPduByIds(Long[] ids)
    {
        return buDataPduMapper.deleteBuDataPduByIds(ids);
    }

    /**
     * 删除PDU设备数据信息
     * 
     * @param id PDU设备数据主键
     * @return 结果
     */
    @Override
    public int deleteBuDataPduById(Long id)
    {
        return buDataPduMapper.deleteBuDataPduById(id);
    }
}
