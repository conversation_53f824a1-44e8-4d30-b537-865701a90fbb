package com.snct.system.service.impl;

import java.util.List;
import com.snct.common.utils.DateUtils;
import com.snct.system.domain.msg.BuMsgAmplifier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.snct.system.mapper.BuMsgAmplifierMapper;
import com.snct.system.service.IBuMsgAmplifierService;

/**
 * 功放消息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
@Service
public class BuMsgAmplifierServiceImpl implements IBuMsgAmplifierService 
{
    @Autowired
    private BuMsgAmplifierMapper buMsgAmplifierMapper;

    /**
     * 查询功放消息
     * 
     * @param id 功放消息主键
     * @return 功放消息
     */
    @Override
    public BuMsgAmplifier selectBuMsgAmplifierById(Long id)
    {
        return buMsgAmplifierMapper.selectBuMsgAmplifierById(id);
    }

    /**
     * 查询功放消息列表
     * 
     * @param buMsgAmplifier 功放消息
     * @return 功放消息
     */
    @Override
    public List<BuMsgAmplifier> selectBuMsgAmplifierList(BuMsgAmplifier buMsgAmplifier)
    {
        return buMsgAmplifierMapper.selectBuMsgAmplifierList(buMsgAmplifier);
    }

    /**
     * 新增功放消息
     * 
     * @param buMsgAmplifier 功放消息
     * @return 结果
     */
    @Override
    public int insertBuMsgAmplifier(BuMsgAmplifier buMsgAmplifier)
    {
        buMsgAmplifier.setCreateTime(DateUtils.getNowDate());
        return buMsgAmplifierMapper.insertBuMsgAmplifier(buMsgAmplifier);
    }

    /**
     * 修改功放消息
     * 
     * @param buMsgAmplifier 功放消息
     * @return 结果
     */
    @Override
    public int updateBuMsgAmplifier(BuMsgAmplifier buMsgAmplifier)
    {
        return buMsgAmplifierMapper.updateBuMsgAmplifier(buMsgAmplifier);
    }

    /**
     * 批量删除功放消息
     * 
     * @param ids 需要删除的功放消息主键
     * @return 结果
     */
    @Override
    public int deleteBuMsgAmplifierByIds(Long[] ids)
    {
        return buMsgAmplifierMapper.deleteBuMsgAmplifierByIds(ids);
    }

    /**
     * 删除功放消息信息
     * 
     * @param id 功放消息主键
     * @return 结果
     */
    @Override
    public int deleteBuMsgAmplifierById(Long id)
    {
        return buMsgAmplifierMapper.deleteBuMsgAmplifierById(id);
    }
}
