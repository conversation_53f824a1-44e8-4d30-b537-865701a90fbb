<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.BuDataAttitudeMapper">
    
    <resultMap type="BuDataAttitude" id="BuDataAttitudeResult">
        <result property="id"    column="id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="deviceCode"    column="device_code"    />
        <result property="deviceName"    column="device_name"    />
        <result property="initialTime"    column="initial_time"    />
        <result property="initialBjTime"    column="initial_bj_time"    />
        <result property="status"    column="status"    />
        <result property="utc"    column="utc"    />
        <result property="lat"    column="lat"    />
        <result property="lon"    column="lon"    />
        <result property="heading"    column="heading"    />
        <result property="rolling"    column="rolling"    />
        <result property="pitch"    column="pitch"    />
        <result property="height"    column="height"    />
        <result property="elpHeight"    column="elp_height"    />
        <result property="distance"    column="distance"    />
        <result property="baselineLength"    column="baseline_length"    />
        <result property="velN"    column="vel_n"    />
        <result property="velE"    column="vel_e"    />
        <result property="velD"    column="vel_d"    />
        <result property="velG"    column="vel_g"    />
        <result property="coordinateNorthing"    column="coordinate_northing"    />
        <result property="coordinateEasting"    column="coordinate_easting"    />
        <result property="northDistance"    column="north_distance"    />
        <result property="eastDistance"    column="east_distance"    />
        <result property="positionIndicator"    column="position_indicator"    />
        <result property="headingIndicator"    column="heading_indicator"    />
        <result property="svn"    column="svn"    />
        <result property="solutionSv"    column="solution_sv"    />
        <result property="diffAge"    column="diff_age"    />
        <result property="serialNo"    column="serial_no"    />
        <result property="stationId"    column="station_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectBuDataAttitudeVo">
        select id, device_id, device_code, device_name, initial_time, initial_bj_time, status, utc, lat, lon, heading, rolling, pitch, height, elp_height, distance, baseline_length, vel_n, vel_e, vel_d, vel_g, coordinate_northing, coordinate_easting, north_distance, east_distance, position_indicator, heading_indicator, svn, solution_sv, diff_age, serial_no, station_id, create_time, update_time from bu_data_attitude
    </sql>

    <select id="selectBuDataAttitudeList" parameterType="BuDataAttitude" resultMap="BuDataAttitudeResult">
        <include refid="selectBuDataAttitudeVo"/>
        <where>  
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="deviceCode != null  and deviceCode != ''"> and device_code = #{deviceCode}</if>
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="initialTime != null "> and initial_time = #{initialTime}</if>
            <if test="initialBjTime != null "> and initial_bj_time = #{initialBjTime}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="utc != null  and utc != ''"> and utc = #{utc}</if>
            <if test="lat != null  and lat != ''"> and lat = #{lat}</if>
            <if test="lon != null  and lon != ''"> and lon = #{lon}</if>
            <if test="heading != null  and heading != ''"> and heading = #{heading}</if>
            <if test="rolling != null  and rolling != ''"> and rolling = #{rolling}</if>
            <if test="pitch != null  and pitch != ''"> and pitch = #{pitch}</if>
            <if test="height != null  and height != ''"> and height = #{height}</if>
            <if test="elpHeight != null  and elpHeight != ''"> and elp_height = #{elpHeight}</if>
            <if test="distance != null  and distance != ''"> and distance = #{distance}</if>
            <if test="baselineLength != null  and baselineLength != ''"> and baseline_length = #{baselineLength}</if>
            <if test="velN != null  and velN != ''"> and vel_n = #{velN}</if>
            <if test="velE != null  and velE != ''"> and vel_e = #{velE}</if>
            <if test="velD != null  and velD != ''"> and vel_d = #{velD}</if>
            <if test="velG != null  and velG != ''"> and vel_g = #{velG}</if>
            <if test="coordinateNorthing != null  and coordinateNorthing != ''"> and coordinate_northing = #{coordinateNorthing}</if>
            <if test="coordinateEasting != null  and coordinateEasting != ''"> and coordinate_easting = #{coordinateEasting}</if>
            <if test="northDistance != null  and northDistance != ''"> and north_distance = #{northDistance}</if>
            <if test="eastDistance != null  and eastDistance != ''"> and east_distance = #{eastDistance}</if>
            <if test="positionIndicator != null  and positionIndicator != ''"> and position_indicator = #{positionIndicator}</if>
            <if test="headingIndicator != null  and headingIndicator != ''"> and heading_indicator = #{headingIndicator}</if>
            <if test="svn != null  and svn != ''"> and svn = #{svn}</if>
            <if test="solutionSv != null  and solutionSv != ''"> and solution_sv = #{solutionSv}</if>
            <if test="diffAge != null  and diffAge != ''"> and diff_age = #{diffAge}</if>
            <if test="serialNo != null  and serialNo != ''"> and serial_no = #{serialNo}</if>
            <if test="stationId != null  and stationId != ''"> and station_id = #{stationId}</if>
        </where>
    </select>
    
    <select id="selectBuDataAttitudeById" parameterType="Long" resultMap="BuDataAttitudeResult">
        <include refid="selectBuDataAttitudeVo"/>
        where id = #{id}
    </select>

    <insert id="insertBuDataAttitude" parameterType="BuDataAttitude" useGeneratedKeys="true" keyProperty="id">
        insert into bu_data_attitude
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">device_id,</if>
            <if test="deviceCode != null and deviceCode != ''">device_code,</if>
            <if test="deviceName != null">device_name,</if>
            <if test="initialTime != null">initial_time,</if>
            <if test="initialBjTime != null">initial_bj_time,</if>
            <if test="status != null">status,</if>
            <if test="utc != null">utc,</if>
            <if test="lat != null">lat,</if>
            <if test="lon != null">lon,</if>
            <if test="heading != null">heading,</if>
            <if test="rolling != null">rolling,</if>
            <if test="pitch != null">pitch,</if>
            <if test="height != null">height,</if>
            <if test="elpHeight != null">elp_height,</if>
            <if test="distance != null">distance,</if>
            <if test="baselineLength != null">baseline_length,</if>
            <if test="velN != null">vel_n,</if>
            <if test="velE != null">vel_e,</if>
            <if test="velD != null">vel_d,</if>
            <if test="velG != null">vel_g,</if>
            <if test="coordinateNorthing != null">coordinate_northing,</if>
            <if test="coordinateEasting != null">coordinate_easting,</if>
            <if test="northDistance != null">north_distance,</if>
            <if test="eastDistance != null">east_distance,</if>
            <if test="positionIndicator != null">position_indicator,</if>
            <if test="headingIndicator != null">heading_indicator,</if>
            <if test="svn != null">svn,</if>
            <if test="solutionSv != null">solution_sv,</if>
            <if test="diffAge != null">diff_age,</if>
            <if test="serialNo != null">serial_no,</if>
            <if test="stationId != null">station_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">#{deviceId},</if>
            <if test="deviceCode != null and deviceCode != ''">#{deviceCode},</if>
            <if test="deviceName != null">#{deviceName},</if>
            <if test="initialTime != null">#{initialTime},</if>
            <if test="initialBjTime != null">#{initialBjTime},</if>
            <if test="status != null">#{status},</if>
            <if test="utc != null">#{utc},</if>
            <if test="lat != null">#{lat},</if>
            <if test="lon != null">#{lon},</if>
            <if test="heading != null">#{heading},</if>
            <if test="rolling != null">#{rolling},</if>
            <if test="pitch != null">#{pitch},</if>
            <if test="height != null">#{height},</if>
            <if test="elpHeight != null">#{elpHeight},</if>
            <if test="distance != null">#{distance},</if>
            <if test="baselineLength != null">#{baselineLength},</if>
            <if test="velN != null">#{velN},</if>
            <if test="velE != null">#{velE},</if>
            <if test="velD != null">#{velD},</if>
            <if test="velG != null">#{velG},</if>
            <if test="coordinateNorthing != null">#{coordinateNorthing},</if>
            <if test="coordinateEasting != null">#{coordinateEasting},</if>
            <if test="northDistance != null">#{northDistance},</if>
            <if test="eastDistance != null">#{eastDistance},</if>
            <if test="positionIndicator != null">#{positionIndicator},</if>
            <if test="headingIndicator != null">#{headingIndicator},</if>
            <if test="svn != null">#{svn},</if>
            <if test="solutionSv != null">#{solutionSv},</if>
            <if test="diffAge != null">#{diffAge},</if>
            <if test="serialNo != null">#{serialNo},</if>
            <if test="stationId != null">#{stationId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBuDataAttitude" parameterType="BuDataAttitude">
        update bu_data_attitude
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="deviceCode != null and deviceCode != ''">device_code = #{deviceCode},</if>
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="initialTime != null">initial_time = #{initialTime},</if>
            <if test="initialBjTime != null">initial_bj_time = #{initialBjTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="utc != null">utc = #{utc},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="lon != null">lon = #{lon},</if>
            <if test="heading != null">heading = #{heading},</if>
            <if test="rolling != null">rolling = #{rolling},</if>
            <if test="pitch != null">pitch = #{pitch},</if>
            <if test="height != null">height = #{height},</if>
            <if test="elpHeight != null">elp_height = #{elpHeight},</if>
            <if test="distance != null">distance = #{distance},</if>
            <if test="baselineLength != null">baseline_length = #{baselineLength},</if>
            <if test="velN != null">vel_n = #{velN},</if>
            <if test="velE != null">vel_e = #{velE},</if>
            <if test="velD != null">vel_d = #{velD},</if>
            <if test="velG != null">vel_g = #{velG},</if>
            <if test="coordinateNorthing != null">coordinate_northing = #{coordinateNorthing},</if>
            <if test="coordinateEasting != null">coordinate_easting = #{coordinateEasting},</if>
            <if test="northDistance != null">north_distance = #{northDistance},</if>
            <if test="eastDistance != null">east_distance = #{eastDistance},</if>
            <if test="positionIndicator != null">position_indicator = #{positionIndicator},</if>
            <if test="headingIndicator != null">heading_indicator = #{headingIndicator},</if>
            <if test="svn != null">svn = #{svn},</if>
            <if test="solutionSv != null">solution_sv = #{solutionSv},</if>
            <if test="diffAge != null">diff_age = #{diffAge},</if>
            <if test="serialNo != null">serial_no = #{serialNo},</if>
            <if test="stationId != null">station_id = #{stationId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBuDataAttitudeById" parameterType="Long">
        delete from bu_data_attitude where id = #{id}
    </delete>

    <delete id="deleteBuDataAttitudeByIds" parameterType="String">
        delete from bu_data_attitude where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>