<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.BuDataAwsMapper">

    <resultMap type="BuDataAws" id="BuDataAwsResult">
        <result property="id" column="id"/>
        <result property="deviceId" column="device_id"/>
        <result property="deviceCode" column="device_code"/>
        <result property="deviceName" column="device_name"/>
        <result property="initialTime" column="initial_time"/>
        <result property="initialBjTime" column="initial_bj_time"/>
        <result property="status" column="status"/>
        <result property="relativeWind" column="relative_wind"/>
        <result property="relativeWindSpeed" column="relative_wind_speed"/>
        <result property="trueWind" column="true_wind"/>
        <result property="trueWindSpeed" column="true_wind_speed"/>
        <result property="windSpeedUnit" column="wind_speed_unit"/>
        <result property="airTemperature" column="air_temperature"/>
        <result property="humidity" column="humidity"/>
        <result property="dewPoint" column="dew_point"/>
        <result property="pressure" column="pressure"/>
        <result property="qfe" column="qfe"/>
        <result property="qnh" column="qnh"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectBuDataAwsVo">
        select id,
               device_id,
               device_code,
               device_name,
               initial_time,
               initial_bj_time,
               status,
               relative_wind,
               relative_wind_speed,
               true_wind,
               true_wind_speed,
               wind_speed_unit,
               air_temperature,
               humidity,
               dew_point,
               pressure,
               qfe,
               qnh,
               create_time,
               update_time
        from bu_data_aws
    </sql>

    <select id="selectBuDataAwsList" parameterType="BuDataAws" resultMap="BuDataAwsResult">
        <include refid="selectBuDataAwsVo"/>
        <where>
            <if test="deviceId != null ">and device_id = #{deviceId}</if>
            <if test="deviceCode != null  and deviceCode != ''">and device_code = #{deviceCode}</if>
            <if test="deviceName != null  and deviceName != ''">and device_name like concat('%', #{deviceName}, '%')
            </if>
            <if test="initialTime != null ">and initial_time = #{initialTime}</if>
            <if test="initialBjTime != null ">and initial_bj_time = #{initialBjTime}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="relativeWind != null ">and relative_wind = #{relativeWind}</if>
            <if test="relativeWindSpeed != null ">and relative_wind_speed = #{relativeWindSpeed}</if>
            <if test="trueWind != null ">and true_wind = #{trueWind}</if>
            <if test="trueWindSpeed != null ">and true_wind_speed = #{trueWindSpeed}</if>
            <if test="windSpeedUnit != null  and windSpeedUnit != ''">and wind_speed_unit = #{windSpeedUnit}</if>
            <if test="airTemperature != null ">and air_temperature = #{airTemperature}</if>
            <if test="humidity != null ">and humidity = #{humidity}</if>
            <if test="dewPoint != null ">and dew_point = #{dewPoint}</if>
            <if test="pressure != null ">and pressure = #{pressure}</if>
            <if test="qfe != null ">and qfe = #{qfe}</if>
            <if test="qnh != null ">and qnh = #{qnh}</if>
        </where>
    </select>

    <select id="selectBuDataAwsById" parameterType="Long" resultMap="BuDataAwsResult">
        <include refid="selectBuDataAwsVo"/>
        where id = #{id}
    </select>

    <insert id="insertBuDataAws" parameterType="BuDataAws" useGeneratedKeys="true" keyProperty="id">
        insert into bu_data_aws
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">device_id,</if>
            <if test="deviceCode != null and deviceCode != ''">device_code,</if>
            <if test="deviceName != null">device_name,</if>
            <if test="initialTime != null">initial_time,</if>
            <if test="initialBjTime != null">initial_bj_time,</if>
            <if test="status != null">status,</if>
            <if test="relativeWind != null">relative_wind,</if>
            <if test="relativeWindSpeed != null">relative_wind_speed,</if>
            <if test="trueWind != null">true_wind,</if>
            <if test="trueWindSpeed != null">true_wind_speed,</if>
            <if test="windSpeedUnit != null">wind_speed_unit,</if>
            <if test="airTemperature != null">air_temperature,</if>
            <if test="humidity != null">humidity,</if>
            <if test="dewPoint != null">dew_point,</if>
            <if test="pressure != null">pressure,</if>
            <if test="qfe != null">qfe,</if>
            <if test="qnh != null">qnh,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">#{deviceId},</if>
            <if test="deviceCode != null and deviceCode != ''">#{deviceCode},</if>
            <if test="deviceName != null">#{deviceName},</if>
            <if test="initialTime != null">#{initialTime},</if>
            <if test="initialBjTime != null">#{initialBjTime},</if>
            <if test="status != null">#{status},</if>
            <if test="relativeWind != null">#{relativeWind},</if>
            <if test="relativeWindSpeed != null">#{relativeWindSpeed},</if>
            <if test="trueWind != null">#{trueWind},</if>
            <if test="trueWindSpeed != null">#{trueWindSpeed},</if>
            <if test="windSpeedUnit != null">#{windSpeedUnit},</if>
            <if test="airTemperature != null">#{airTemperature},</if>
            <if test="humidity != null">#{humidity},</if>
            <if test="dewPoint != null">#{dewPoint},</if>
            <if test="pressure != null">#{pressure},</if>
            <if test="qfe != null">#{qfe},</if>
            <if test="qnh != null">#{qnh},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateBuDataAws" parameterType="BuDataAws">
        update bu_data_aws
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="deviceCode != null and deviceCode != ''">device_code = #{deviceCode},</if>
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="initialTime != null">initial_time = #{initialTime},</if>
            <if test="initialBjTime != null">initial_bj_time = #{initialBjTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="relativeWind != null">relative_wind = #{relativeWind},</if>
            <if test="relativeWindSpeed != null">relative_wind_speed = #{relativeWindSpeed},</if>
            <if test="trueWind != null">true_wind = #{trueWind},</if>
            <if test="trueWindSpeed != null">true_wind_speed = #{trueWindSpeed},</if>
            <if test="windSpeedUnit != null">wind_speed_unit = #{windSpeedUnit},</if>
            <if test="airTemperature != null">air_temperature = #{airTemperature},</if>
            <if test="humidity != null">humidity = #{humidity},</if>
            <if test="dewPoint != null">dew_point = #{dewPoint},</if>
            <if test="pressure != null">pressure = #{pressure},</if>
            <if test="qfe != null">qfe = #{qfe},</if>
            <if test="qnh != null">qnh = #{qnh},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBuDataAwsById" parameterType="Long">
        delete
        from bu_data_aws
        where id = #{id}
    </delete>

    <delete id="deleteBuDataAwsByIds" parameterType="String">
        delete from bu_data_aws where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteBuDataAwsByDate" parameterType="String">
        delete from bu_data_aws where initial_bj_time &lt; #{cutoffDate}
    </delete>
</mapper>