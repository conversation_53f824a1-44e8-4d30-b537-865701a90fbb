<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.BuDataGpsMapper">

    <resultMap type="BuDataGps" id="BuDataGpsResult">
        <result property="id" column="id"/>
        <result property="deviceId" column="device_id"/>
        <result property="deviceCode" column="device_code"/>
        <result property="deviceName" column="device_name"/>
        <result property="initialTime" column="initial_time"/>
        <result property="initialBjTime" column="initial_bj_time"/>
        <result property="status" column="status"/>
        <result property="utcTime" column="utc_time"/>
        <result property="latitude" column="latitude"/>
        <result property="longitude" column="longitude"/>
        <result property="latitudeHemisphere" column="latitude_hemisphere"/>
        <result property="longitudeHemisphere" column="longitude_hemisphere"/>
        <result property="positionQuality" column="position_quality"/>
        <result property="satellitesCount" column="satellites_count"/>
        <result property="horizontalAccuracy" column="horizontal_accuracy"/>
        <result property="antennaHeight" column="antenna_height"/>
        <result property="speed" column="speed"/>
        <result property="course" column="course"/>
        <result property="gpsDate" column="gps_date"/>
        <result property="day" column="day"/>
        <result property="month" column="month"/>
        <result property="year" column="year"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectBuDataGpsVo">
        select id,
               device_id,
               device_code,
               device_name,
               initial_time,
               initial_bj_time,
               status,
               utc_time,
               latitude,
               longitude,
               latitude_hemisphere,
               longitude_hemisphere,
               position_quality,
               satellites_count,
               horizontal_accuracy,
               antenna_height,
               speed,
               course,
               gps_date, day, month, year, create_time, update_time
        from bu_data_gps
    </sql>

    <select id="selectBuDataGpsList" parameterType="BuDataGps" resultMap="BuDataGpsResult">
        <include refid="selectBuDataGpsVo"/>
        <where>
            <if test="deviceId != null ">and device_id = #{deviceId}</if>
            <if test="deviceCode != null  and deviceCode != ''">and device_code = #{deviceCode}</if>
            <if test="deviceName != null  and deviceName != ''">and device_name like concat('%', #{deviceName}, '%')
            </if>
            <if test="initialTime != null ">and initial_time = #{initialTime}</if>
            <if test="initialBjTime != null ">and initial_bj_time = #{initialBjTime}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="utcTime != null  and utcTime != ''">and utc_time = #{utcTime}</if>
            <if test="latitude != null ">and latitude = #{latitude}</if>
            <if test="longitude != null ">and longitude = #{longitude}</if>
            <if test="latitudeHemisphere != null  and latitudeHemisphere != ''">and latitude_hemisphere =
                #{latitudeHemisphere}
            </if>
            <if test="longitudeHemisphere != null  and longitudeHemisphere != ''">and longitude_hemisphere =
                #{longitudeHemisphere}
            </if>
            <if test="positionQuality != null ">and position_quality = #{positionQuality}</if>
            <if test="satellitesCount != null ">and satellites_count = #{satellitesCount}</if>
            <if test="horizontalAccuracy != null ">and horizontal_accuracy = #{horizontalAccuracy}</if>
            <if test="antennaHeight != null ">and antenna_height = #{antennaHeight}</if>
            <if test="speed != null ">and speed = #{speed}</if>
            <if test="course != null ">and course = #{course}</if>
            <if test="gpsDate != null ">and gps_date = #{gpsDate}</if>
            <if test="day != null ">and day = #{day}</if>
            <if test="month != null ">and month = #{month}</if>
            <if test="year != null ">and year = #{year}</if>
        </where>
    </select>

    <select id="selectBuDataGpsById" parameterType="Long" resultMap="BuDataGpsResult">
        <include refid="selectBuDataGpsVo"/>
        where id = #{id}
    </select>

    <insert id="insertBuDataGps" parameterType="BuDataGps" useGeneratedKeys="true" keyProperty="id">
        insert into bu_data_gps
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">device_id,</if>
            <if test="deviceCode != null and deviceCode != ''">device_code,</if>
            <if test="deviceName != null">device_name,</if>
            <if test="initialTime != null">initial_time,</if>
            <if test="initialBjTime != null">initial_bj_time,</if>
            <if test="status != null">status,</if>
            <if test="utcTime != null">`utc_time`,</if>
            <if test="latitude != null">latitude,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitudeHemisphere != null">latitude_hemisphere,</if>
            <if test="longitudeHemisphere != null">longitude_hemisphere,</if>
            <if test="positionQuality != null">position_quality,</if>
            <if test="satellitesCount != null">satellites_count,</if>
            <if test="horizontalAccuracy != null">horizontal_accuracy,</if>
            <if test="antennaHeight != null">antenna_height,</if>
            <if test="speed != null">speed,</if>
            <if test="course != null">course,</if>
            <if test="gpsDate != null">gps_date,</if>
            <if test="day != null">day,</if>
            <if test="month != null">month,</if>
            <if test="year != null">year,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">#{deviceId},</if>
            <if test="deviceCode != null and deviceCode != ''">#{deviceCode},</if>
            <if test="deviceName != null">#{deviceName},</if>
            <if test="initialTime != null">#{initialTime},</if>
            <if test="initialBjTime != null">#{initialBjTime},</if>
            <if test="status != null">#{status},</if>
            <if test="utcTime != null">#{utcTime},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitudeHemisphere != null">#{latitudeHemisphere},</if>
            <if test="longitudeHemisphere != null">#{longitudeHemisphere},</if>
            <if test="positionQuality != null">#{positionQuality},</if>
            <if test="satellitesCount != null">#{satellitesCount},</if>
            <if test="horizontalAccuracy != null">#{horizontalAccuracy},</if>
            <if test="antennaHeight != null">#{antennaHeight},</if>
            <if test="speed != null">#{speed},</if>
            <if test="course != null">#{course},</if>
            <if test="gpsDate != null">#{gpsDate},</if>
            <if test="day != null">#{day},</if>
            <if test="month != null">#{month},</if>
            <if test="year != null">#{year},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateBuDataGps" parameterType="BuDataGps">
        update bu_data_gps
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="deviceCode != null and deviceCode != ''">device_code = #{deviceCode},</if>
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="initialTime != null">initial_time = #{initialTime},</if>
            <if test="initialBjTime != null">initial_bj_time = #{initialBjTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="utcTime != null">utc_time = #{utcTime},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitudeHemisphere != null">latitude_hemisphere = #{latitudeHemisphere},</if>
            <if test="longitudeHemisphere != null">longitude_hemisphere = #{longitudeHemisphere},</if>
            <if test="positionQuality != null">position_quality = #{positionQuality},</if>
            <if test="satellitesCount != null">satellites_count = #{satellitesCount},</if>
            <if test="horizontalAccuracy != null">horizontal_accuracy = #{horizontalAccuracy},</if>
            <if test="antennaHeight != null">antenna_height = #{antennaHeight},</if>
            <if test="speed != null">speed = #{speed},</if>
            <if test="course != null">course = #{course},</if>
            <if test="gpsDate != null">gps_date = #{gpsDate},</if>
            <if test="day != null">day = #{day},</if>
            <if test="month != null">month = #{month},</if>
            <if test="year != null">year = #{year},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBuDataGpsById" parameterType="Long">
        delete
        from bu_data_gps
        where id = #{id}
    </delete>

    <delete id="deleteBuDataGpsByIds" parameterType="String">
        delete from bu_data_gps where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteBuDataGpsByDate" parameterType="String">
        delete from bu_data_gps where initial_bj_time &lt; #{cutoffDate}
    </delete>
</mapper>