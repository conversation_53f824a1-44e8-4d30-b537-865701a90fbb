<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.BuDataPduMapper">
    
    <resultMap type="BuDataPdu" id="BuDataPduResult">
        <result property="id"    column="id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="deviceCode"    column="device_code"    />
        <result property="deviceName"    column="device_name"    />
        <result property="initialTime"    column="initial_time"    />
        <result property="initialBjTime"    column="initial_bj_time"    />
        <result property="status"    column="status"    />
        <result property="electric1"    column="electric1"    />
        <result property="power1"    column="power1"    />
        <result property="status1"    column="status1"    />
        <result property="electric2"    column="electric2"    />
        <result property="power2"    column="power2"    />
        <result property="status2"    column="status2"    />
        <result property="electric3"    column="electric3"    />
        <result property="power3"    column="power3"    />
        <result property="status3"    column="status3"    />
        <result property="electric4"    column="electric4"    />
        <result property="power4"    column="power4"    />
        <result property="status4"    column="status4"    />
        <result property="electric5"    column="electric5"    />
        <result property="power5"    column="power5"    />
        <result property="status5"    column="status5"    />
        <result property="electric6"    column="electric6"    />
        <result property="power6"    column="power6"    />
        <result property="status6"    column="status6"    />
        <result property="electric7"    column="electric7"    />
        <result property="power7"    column="power7"    />
        <result property="status7"    column="status7"    />
        <result property="electric8"    column="electric8"    />
        <result property="power8"    column="power8"    />
        <result property="status8"    column="status8"    />
        <result property="electric"    column="electric"    />
        <result property="manage"    column="manage"    />
        <result property="voltage"    column="voltage"    />
        <result property="yesPower"    column="yes_power"    />
        <result property="noPower"    column="no_power"    />
        <result property="seePower"    column="see_power"    />
        <result property="powerParam"    column="power_param"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectBuDataPduVo">
        select id, device_id, device_code, device_name, initial_time, initial_bj_time, status, electric1, power1, status1, electric2, power2, status2, electric3, power3, status3, electric4, power4, status4, electric5, power5, status5, electric6, power6, status6, electric7, power7, status7, electric8, power8, status8, electric, manage, voltage, yes_power, no_power, see_power, power_param, create_time, update_time from bu_data_pdu
    </sql>

    <select id="selectBuDataPduList" parameterType="BuDataPdu" resultMap="BuDataPduResult">
        <include refid="selectBuDataPduVo"/>
        <where>  
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="deviceCode != null  and deviceCode != ''"> and device_code = #{deviceCode}</if>
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="initialTime != null "> and initial_time = #{initialTime}</if>
            <if test="initialBjTime != null "> and initial_bj_time = #{initialBjTime}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="electric1 != null "> and electric1 = #{electric1}</if>
            <if test="power1 != null "> and power1 = #{power1}</if>
            <if test="status1 != null "> and status1 = #{status1}</if>
            <if test="electric2 != null "> and electric2 = #{electric2}</if>
            <if test="power2 != null "> and power2 = #{power2}</if>
            <if test="status2 != null "> and status2 = #{status2}</if>
            <if test="electric3 != null "> and electric3 = #{electric3}</if>
            <if test="power3 != null "> and power3 = #{power3}</if>
            <if test="status3 != null "> and status3 = #{status3}</if>
            <if test="electric4 != null "> and electric4 = #{electric4}</if>
            <if test="power4 != null "> and power4 = #{power4}</if>
            <if test="status4 != null "> and status4 = #{status4}</if>
            <if test="electric5 != null "> and electric5 = #{electric5}</if>
            <if test="power5 != null "> and power5 = #{power5}</if>
            <if test="status5 != null "> and status5 = #{status5}</if>
            <if test="electric6 != null "> and electric6 = #{electric6}</if>
            <if test="power6 != null "> and power6 = #{power6}</if>
            <if test="status6 != null "> and status6 = #{status6}</if>
            <if test="electric7 != null "> and electric7 = #{electric7}</if>
            <if test="power7 != null "> and power7 = #{power7}</if>
            <if test="status7 != null "> and status7 = #{status7}</if>
            <if test="electric8 != null "> and electric8 = #{electric8}</if>
            <if test="power8 != null "> and power8 = #{power8}</if>
            <if test="status8 != null "> and status8 = #{status8}</if>
            <if test="electric != null "> and electric = #{electric}</if>
            <if test="manage != null "> and manage = #{manage}</if>
            <if test="voltage != null "> and voltage = #{voltage}</if>
            <if test="yesPower != null "> and yes_power = #{yesPower}</if>
            <if test="noPower != null "> and no_power = #{noPower}</if>
            <if test="seePower != null "> and see_power = #{seePower}</if>
            <if test="powerParam != null "> and power_param = #{powerParam}</if>
        </where>
    </select>
    
    <select id="selectBuDataPduById" parameterType="Long" resultMap="BuDataPduResult">
        <include refid="selectBuDataPduVo"/>
        where id = #{id}
    </select>

    <insert id="insertBuDataPdu" parameterType="BuDataPdu" useGeneratedKeys="true" keyProperty="id">
        insert into bu_data_pdu
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">device_id,</if>
            <if test="deviceCode != null and deviceCode != ''">device_code,</if>
            <if test="deviceName != null">device_name,</if>
            <if test="initialTime != null">initial_time,</if>
            <if test="initialBjTime != null">initial_bj_time,</if>
            <if test="status != null">status,</if>
            <if test="electric1 != null">electric1,</if>
            <if test="power1 != null">power1,</if>
            <if test="status1 != null">status1,</if>
            <if test="electric2 != null">electric2,</if>
            <if test="power2 != null">power2,</if>
            <if test="status2 != null">status2,</if>
            <if test="electric3 != null">electric3,</if>
            <if test="power3 != null">power3,</if>
            <if test="status3 != null">status3,</if>
            <if test="electric4 != null">electric4,</if>
            <if test="power4 != null">power4,</if>
            <if test="status4 != null">status4,</if>
            <if test="electric5 != null">electric5,</if>
            <if test="power5 != null">power5,</if>
            <if test="status5 != null">status5,</if>
            <if test="electric6 != null">electric6,</if>
            <if test="power6 != null">power6,</if>
            <if test="status6 != null">status6,</if>
            <if test="electric7 != null">electric7,</if>
            <if test="power7 != null">power7,</if>
            <if test="status7 != null">status7,</if>
            <if test="electric8 != null">electric8,</if>
            <if test="power8 != null">power8,</if>
            <if test="status8 != null">status8,</if>
            <if test="electric != null">electric,</if>
            <if test="manage != null">manage,</if>
            <if test="voltage != null">voltage,</if>
            <if test="yesPower != null">yes_power,</if>
            <if test="noPower != null">no_power,</if>
            <if test="seePower != null">see_power,</if>
            <if test="powerParam != null">power_param,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">#{deviceId},</if>
            <if test="deviceCode != null and deviceCode != ''">#{deviceCode},</if>
            <if test="deviceName != null">#{deviceName},</if>
            <if test="initialTime != null">#{initialTime},</if>
            <if test="initialBjTime != null">#{initialBjTime},</if>
            <if test="status != null">#{status},</if>
            <if test="electric1 != null">#{electric1},</if>
            <if test="power1 != null">#{power1},</if>
            <if test="status1 != null">#{status1},</if>
            <if test="electric2 != null">#{electric2},</if>
            <if test="power2 != null">#{power2},</if>
            <if test="status2 != null">#{status2},</if>
            <if test="electric3 != null">#{electric3},</if>
            <if test="power3 != null">#{power3},</if>
            <if test="status3 != null">#{status3},</if>
            <if test="electric4 != null">#{electric4},</if>
            <if test="power4 != null">#{power4},</if>
            <if test="status4 != null">#{status4},</if>
            <if test="electric5 != null">#{electric5},</if>
            <if test="power5 != null">#{power5},</if>
            <if test="status5 != null">#{status5},</if>
            <if test="electric6 != null">#{electric6},</if>
            <if test="power6 != null">#{power6},</if>
            <if test="status6 != null">#{status6},</if>
            <if test="electric7 != null">#{electric7},</if>
            <if test="power7 != null">#{power7},</if>
            <if test="status7 != null">#{status7},</if>
            <if test="electric8 != null">#{electric8},</if>
            <if test="power8 != null">#{power8},</if>
            <if test="status8 != null">#{status8},</if>
            <if test="electric != null">#{electric},</if>
            <if test="manage != null">#{manage},</if>
            <if test="voltage != null">#{voltage},</if>
            <if test="yesPower != null">#{yesPower},</if>
            <if test="noPower != null">#{noPower},</if>
            <if test="seePower != null">#{seePower},</if>
            <if test="powerParam != null">#{powerParam},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBuDataPdu" parameterType="BuDataPdu">
        update bu_data_pdu
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="deviceCode != null and deviceCode != ''">device_code = #{deviceCode},</if>
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="initialTime != null">initial_time = #{initialTime},</if>
            <if test="initialBjTime != null">initial_bj_time = #{initialBjTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="electric1 != null">electric1 = #{electric1},</if>
            <if test="power1 != null">power1 = #{power1},</if>
            <if test="status1 != null">status1 = #{status1},</if>
            <if test="electric2 != null">electric2 = #{electric2},</if>
            <if test="power2 != null">power2 = #{power2},</if>
            <if test="status2 != null">status2 = #{status2},</if>
            <if test="electric3 != null">electric3 = #{electric3},</if>
            <if test="power3 != null">power3 = #{power3},</if>
            <if test="status3 != null">status3 = #{status3},</if>
            <if test="electric4 != null">electric4 = #{electric4},</if>
            <if test="power4 != null">power4 = #{power4},</if>
            <if test="status4 != null">status4 = #{status4},</if>
            <if test="electric5 != null">electric5 = #{electric5},</if>
            <if test="power5 != null">power5 = #{power5},</if>
            <if test="status5 != null">status5 = #{status5},</if>
            <if test="electric6 != null">electric6 = #{electric6},</if>
            <if test="power6 != null">power6 = #{power6},</if>
            <if test="status6 != null">status6 = #{status6},</if>
            <if test="electric7 != null">electric7 = #{electric7},</if>
            <if test="power7 != null">power7 = #{power7},</if>
            <if test="status7 != null">status7 = #{status7},</if>
            <if test="electric8 != null">electric8 = #{electric8},</if>
            <if test="power8 != null">power8 = #{power8},</if>
            <if test="status8 != null">status8 = #{status8},</if>
            <if test="electric != null">electric = #{electric},</if>
            <if test="manage != null">manage = #{manage},</if>
            <if test="voltage != null">voltage = #{voltage},</if>
            <if test="yesPower != null">yes_power = #{yesPower},</if>
            <if test="noPower != null">no_power = #{noPower},</if>
            <if test="seePower != null">see_power = #{seePower},</if>
            <if test="powerParam != null">power_param = #{powerParam},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBuDataPduById" parameterType="Long">
        delete from bu_data_pdu where id = #{id}
    </delete>

    <delete id="deleteBuDataPduByIds" parameterType="String">
        delete from bu_data_pdu where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteBuDataPduByDate" parameterType="String">
        delete from bu_data_pdu where initial_bj_time &lt; #{cutoffDate}
    </delete>
</mapper>