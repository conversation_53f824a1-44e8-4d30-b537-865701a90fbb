<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.BuMsgModemMapper">
    
    <resultMap type="BuMsgModem" id="BuMsgModemResult">
        <result property="id"    column="id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="signal"    column="signal"    />
        <result property="speed"    column="speed"    />
        <result property="sendPower"    column="sendPower"    />
        <result property="isFlag"    column="isFlag"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectBuMsgModemVo">
        select id, device_id, signal, speed, sendPower, isFlag, status, create_time from bu_msg_modem
    </sql>

    <select id="selectBuMsgModemList" parameterType="BuMsgModem" resultMap="BuMsgModemResult">
        <include refid="selectBuMsgModemVo"/>
        <where>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="signal != null "> and signal = #{signal}</if>
            <if test="speed != null "> and speed = #{speed}</if>
            <if test="sendPower != null "> and sendPower = #{sendPower}</if>
            <if test="isFlag != null "> and isFlag = #{isFlag}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectBuMsgModemById" parameterType="Long" resultMap="BuMsgModemResult">
        <include refid="selectBuMsgModemVo"/>
        where id = #{id}
    </select>

    <insert id="insertBuMsgModem" parameterType="BuMsgModem" useGeneratedKeys="true" keyProperty="id">
        insert into bu_msg_modem
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">device_id,</if>
            <if test="signal != null">signal,</if>
            <if test="speed != null">speed,</if>
            <if test="sendPower != null">sendPower,</if>
            <if test="isFlag != null">isFlag,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">#{deviceId},</if>
            <if test="signal != null">#{signal},</if>
            <if test="speed != null">#{speed},</if>
            <if test="sendPower != null">#{sendPower},</if>
            <if test="isFlag != null">#{isFlag},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateBuMsgModem" parameterType="BuMsgModem">
        update bu_msg_modem
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="signal != null">signal = #{signal},</if>
            <if test="speed != null">speed = #{speed},</if>
            <if test="sendPower != null">sendPower = #{sendPower},</if>
            <if test="isFlag != null">isFlag = #{isFlag},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBuMsgModemById" parameterType="Long">
        delete from bu_msg_modem where id = #{id}
    </delete>

    <delete id="deleteBuMsgModemByIds" parameterType="String">
        delete from bu_msg_modem where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>