<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.DefaultAttributeMapper">

	<resultMap type="DefaultAttribute" id="DefaultAttributeResult">
		<result property="id"    column="id"    />
		<result property="type"    column="type"    />
		<result property="name"    column="name"    />
	</resultMap>

	<sql id="selectDefaultAttributeVo">
		select id, type, name from bu_default_attribute
	</sql>

	<select id="selectDefaultAttributeList" parameterType="DefaultAttribute" resultMap="DefaultAttributeResult">
		<include refid="selectDefaultAttributeVo"/>
		<where>
			<if test="type != null "> and type = #{type}</if>
			<if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
		</where>
	</select>

	<select id="selectDefaultAttributeById" parameterType="Long" resultMap="DefaultAttributeResult">
		<include refid="selectDefaultAttributeVo"/>
		where id = #{id}
	</select>

	<insert id="insertDefaultAttribute" parameterType="DefaultAttribute" useGeneratedKeys="true" keyProperty="id">
		insert into bu_default_attribute
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="type != null">type,</if>
			<if test="name != null">name,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="type != null">#{type},</if>
			<if test="name != null">#{name},</if>
		</trim>
	</insert>

	<update id="updateDefaultAttribute" parameterType="DefaultAttribute">
		update bu_default_attribute
		<trim prefix="SET" suffixOverrides=",">
			<if test="type != null">type = #{type},</if>
			<if test="name != null">name = #{name},</if>
		</trim>
		where id = #{id}
	</update>

	<delete id="deleteDefaultAttributeById" parameterType="Long">
		delete from bu_default_attribute where id = #{id}
	</delete>

	<delete id="deleteDefaultAttributeByIds" parameterType="String">
		delete from bu_default_attribute where id in
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>
</mapper>